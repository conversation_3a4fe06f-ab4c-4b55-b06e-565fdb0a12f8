package com.weihengtech.ieee.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 系列枚举
 *
 * <AUTHOR>
 * @date 2023/11/8 19:34
 * @version 1.0
 */
@Getter
@AllArgsConstructor
public enum SeriesEnum {
	Single_phase("101", "Single-phase"),
	Three_phase("102", "Three-phase"),
	NA_Device("103", "NA-Device"),
	AC_Charger("104", "AC-Charger"),
	Smart_Plug("105", "Smart-Plug");

	final String id;
	final String name;
}
