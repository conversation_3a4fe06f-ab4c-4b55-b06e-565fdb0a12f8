package com.weihengtech.ieee.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 电网公司枚举
 *
 * <AUTHOR>
 * @date 2023/11/8 19:34
 * @version 1.0
 */
@Getter
@AllArgsConstructor
public enum GridCompanyEnum {
	SAPowerNetworks(0, "SAPowerNetworks"),
	<PERSON><PERSON><PERSON>(1, "<PERSON><PERSON><PERSON>"),
	CitiPower(2, "CitiPower"),
	AusNet(3, "AusNet"),
	Synergy(4, "Synergy"),
	ForTest(255, "ForTest");

	final int id;
	final String name;
}
