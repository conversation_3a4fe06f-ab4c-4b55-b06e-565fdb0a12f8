syntax = "proto3";

import "empty.proto";

package rpc;

option java_multiple_files = false;
option java_package = "com.weihengtech.ieee";
option java_outer_classname = "EndDeviceProto";

service EndDeviceService {
  rpc GetEndDevices (GetEndDevicesParam) returns (GetEndDeviceResponse) {}
  rpc GetEndDevice (GetEndDeviceParam) returns (EndDevice) {}
  rpc GetEndDeviceData (GetEndDeviceParam) returns (EndDeviceData) {}
  rpc SetDefaultControl(DefaultDERControl) returns (Empty) {}
  rpc SetControl(DERControlRequest) returns (Empty) {}
  rpc UpdateStepState(StepStateRequest) returns (Empty) {}
  rpc SubscribeEndDevice(Empty) returns (stream SubscribeEndDeviceResponse) {}
  rpc UpdateUtilityServerStatus(UtilityServerStatus) returns (Empty) {}
}

//  SAPowerNetworks = 0;
//  Jemena = 1;
//  CitiPower = 2;
//  AusNet = 3;
//  Synergy = 4;
//  ForTest = 255;
message GetEndDevicesParam {
  uint32 electric_power_company = 1;
}

message GetEndDeviceParam {
  int64 id = 1;
}

message GetEndDeviceResponse {
  repeated EndDevice end_devices = 1;
}

message EndDevice {
  int64 id = 1;
  string lfdi = 2;
  uint64 sfdi = 3;
  uint32 pin = 4;
  int64 design_max_power = 5; // 单位 w
  string nmi = 6;
  // 0 - Not applicable / Unknown
  // 1 - Virtual or mixed DER
  // 2 - Reciprocating engine
  // 3 - Fuel cell
  // 4 - Photovoltaic system
  // 5 - Combined heat and power
  // 6 - Other generation system
  // 80 - Other storage system
  // 81 - Electric vehicle
  // 82 - EVSE
  // 83 - Combined PV and storage
  DERType der_type = 7;
  // 0 = Programmable communicating thermostat
  // 1 = Strip heaters
  // 2 = Baseboard heaters
  // 3 = Water heater
  // 4 = Pool pump
  // 5 = Sauna
  // 6 = Hot tub
  // 7 = Smart appliance
  // 8 = Irrigation pump
  // 9 = Managed commercial and industrial (C&I) loads
  // 10 = Simple misc. (residential on/off) loads
  // 11 = Exterior lighting
  // 12 = Interior lighting
  // 13 = Load control switch
  // 14 = Energy management system
  // 15 = Smart energy module
  // 16 = Electric vehicle
  // 17 = Electric vehicle supply equipment (EVSE)
  // 18 = Virtual or mixed DER
  // 19 = Reciprocating engine
  // 20 = Fuel cell
  // 21 = Photovoltaic system
  // 22 = Combined heat and power
  // 23 = Combined PV and storage
  // 24 = Other generation system
  // 25 = Other storage system
  uint32 device_category = 8;
  DERControlMode control_mode = 9;
  DoeDERControlMode doe_control_mode = 10;
  float rtgMaxChargeRateW = 11; // 单位 w
  float rtgMaxDischargeRateW = 12; // 单位 w
  float rtgMaxVA = 13; // 单位 VA
  float rtgMaxVar = 14; // 单位 var
  float rtgMaxVarNeg = 15; // 单位 var
  float rtgMinPFOverExcited = 16;
  float rtgMinPFUnderExcited = 17;
  float rtgMaxWh = 18; // 单位 Wh

  // For Synergy
  VPPDERControlMode vpp_control_mode = 35;
}

message SubscribeEndDeviceResponse {
  EndDevice end_device = 1;
  SubscriptionType type = 2;
  uint32 electric_power_company = 3;
}

enum SubscriptionType {
  SubscriptionDelete = 0;
  SubscriptionCreate = 1;
  SubscriptionUpdate = 2;
}

message DERControlMode {
  bool charge_mode = 1;
  bool discharge_mode = 2;
  bool opModConnect = 3;
  bool opModEnergize = 4;
  bool opModFixedPFAbsorbW = 6;
  bool opModFixedPFInjectW = 7;
  bool opModFixedW = 9;
  bool opModFreqWatt = 10;
  bool opModHFRTMayTrip = 11;
  bool opModHFRTMustTrip = 12;
  bool opModHVRTMayTrip = 13;
  bool opModHVRTMomentaryCessation = 14;
  bool opModHVRTMustTrip = 15;
  bool opModLFRTMayTrip = 16;
  bool opModLFRTMustTrip = 17;
  bool opModLVRTMayTrip = 18;
  bool opModLVRTMustTrip = 19;
  bool opModLVRTMomentaryCessation = 8;
  bool opModTargetW = 20;
  bool opModVoltVar = 21;
  bool opModVoltWatt = 22;
  bool opModMaxLimW = 5;
}

message DoeDERControlMode {
  bool opModExpLimW = 1;
  bool opModImpLimW = 2;
  bool opModGenLimW = 3;
  bool opModLoadLimW = 4;
}

message VPPDERControlMode {
  bool OpModStorageTargetW = 1;
}

enum DERType {
  DERTypeUnknown = 0;
  DERTypeVirtual = 1;
  DERTypeReciprocatingEngine = 2;
  DERTypeFuelCell = 3;
  DERTypePhotovoltaicSystem = 4;
  DERTypeCombinedHeatAndPower = 5;
  DERTypeOtherGenerationSystem = 6;
  DERTypeOtherStorageSystem = 80;
  DERTypeElectricVehicle = 81;
  DERTypeEVSE = 82;
  DERTypeCombinedPVAndStorage = 83;
}

message EndDeviceData {
  string lfdi = 12;
  double active_power = 1; // 单位 w 充电功率 当为正数时表示充电
  double reactive_power = 2; // 单位 var
  double pcs_active_power = 20; // 单位 w 充电功率 当为正数时表示放电
  double pcs_reactive_power = 22; // 单位 var
  double voltage_an = 3; // 单位 v
  double voltage_bn = 4; // 单位 v
  double voltage_cn = 5; // 单位 v
  double frequency = 8; // 单位 Hz
  uint32 set_max_power = 9; // 单位 w
  uint32 set_grad_w = 10; // 功率速率 百分比
  float setMaxChargeRateW = 23; // 单位 w
  float setMaxDischargeRateW = 24; // 单位 w
  float setMaxVA = 25; // 单位 VA
  float setMaxVar = 26; // 单位 var
  float setMaxVarNeg = 27; // 单位 var
  float setMinPFOverExcited = 28;
  float setMinPFUnderExcited = 29;
  float setMaxWh = 30; // 单位 Wh
  DoeDERControlMode doe_control_mode = 11;
  ConnectType generator = 13;
  ConnectType stor = 14;
  // DER OperationalModeStatus value:
  // 0 - Not applicable / Unknown
  // 1 - Off
  // 2 - Operational mode
  // 3 - Test mode
  uint32 operational_mode = 15;
  Alarm alarm = 16;
  float soc = 17; // 单位 %
  DeviceType device_type = 18;
  //1 - 关闭
  //2 - 睡眠（自动关机）或 DER 输出功率/电压低
  //3 - 启动中或开启但未生产功率
  //4 - 跟踪最大功率点（MPPT）
  //5 - 强制功率减少/降额
  //6 - 关机中
  //7 - 存在一个或多个故障
  //8 - 待机（设备服务中） - DER 可能处于高输出电压/功率状态
  //9 - 测试模式
  uint32 inverterStatus = 6;

  // TODO 以下字段待确认
  float availabilityDuration = 7; // 单位 s
  float maxChargeDuration = 31; // 单位 s
  float reserveChargePercent = 32; // 单位 %
  float reservePercent = 33; // 单位 %
  float statVarAvail = 34; // 单位 var
  float statWAvail = 35; // 单位 w

  // For Synergy
  VPPDERControlMode vpp_control_mode = 54;
  float setMinWh = 55; // 单位 Wh
  float storedEnergy = 56; // 单位 Wh
}

// 0x11:single-phase inverter
// 0x13:three-phase inverter
// 0x21:single phase storage-HV
// 0x22:single phase hybrid-HV
// 0x23:three phase storage-HV
// 0x24:three phase hybrid-HV
// 0x25:single phase hybrid-US/EU
// 0x31:single phase storage-LV
// 0x33:three phase storage-LV
enum DeviceType {
  DeviceTypeUnknown = 0;
  DeviceTypeSinglePhaseInverter = 0x11;
  DeviceTypeThreePhaseInverter = 0x13;
  DeviceTypeSinglePhaseStorageHV = 0x21;
  DeviceTypeSinglePhaseHybridHV = 0x22;
  DeviceTypeThreePhaseStorageHV = 0x23;
  DeviceTypeThreePhaseHybridHV = 0x24;
  DeviceTypeSinglePhaseHybridUSEU = 0x25;
  DeviceTypeSinglePhaseStorageLV = 0x31;
  DeviceTypeThreePhaseStorageLV = 0x33;
}

// 0 = DER_FAULT_OVER_CURRENT
// 1 = DER_FAULT_OVER_VOLTAGE
// 2 = DER_FAULT_UNDER_VOLTAGE
// 3 = DER_FAULT_OVER_FREQUENCY
// 4 = DER_FAULT_UNDER_FREQUENCY
// 5 = DER_FAULT_VOLTAGE_IMBALANCE
// 6 = DER_FAULT_CURRENT_IMBALANCE
// 7 = DER_FAULT_EMERGENCY_LOCAL
// 8 = DER_FAULT_EMERGENCY_REMOTE
// 9 = DER_FAULT_LOW_POWER_INPUT
// 10 = DER_FAULT_PHASE_ROTATION
// 11 to 31 = Reserved
message Alarm {
  bool over_current = 1;
  bool over_voltage = 2;
  bool under_voltage = 3;
  bool over_frequency = 4;
  bool under_frequency = 5;
  bool voltage_imbalance = 6;
  bool current_imbalance = 7;
  bool emergency_local = 8;
  bool emergency_remote = 9;
  bool low_power_input = 10;
  bool phase_rotation = 11;
}

// ConnectType bitmap 0b0000 0000
// 0b0000 0001 连接
// 0b0000 0010 可用
message ConnectType {
  bool Connected = 1;
  bool Available = 2;
  bool Operating = 3;
  bool Test = 4;
  bool Fault = 5;
}

enum YRefType {
  YRefTypeNone = 0;
  YRefTypeSetMaxW = 1;
  YRefTypeSetMaxVar = 2;
  YRefTypeStatVarAvail = 3;
  YRefTypeSetEffectiveV = 4;
  YRefTypeSetMaxChargeRateW = 5;
  YRefTypeSetMaxDischargeRateW = 6;
  YRefTypeStatWAvail = 7;
}

message Curve {
  repeated Point points = 1;
  uint32 openLoopTms = 2;
  uint32 rampDecTms = 3;
  uint32 rampIncTms = 4;
  uint32 rampPT1Tms = 5;
  YRefType yRefType = 6;
}

message Point {
  float x = 1;
  float y = 2;
  bool excitation = 3;
}

message PowerFactor {
  float value = 1;
  bool excitation = 2;
}

message FixedPower {
  uint32 value = 1;
  // 0 - N/A
  // 1 - %setMaxW
  // 2 - %setMaxVar
  // 3 - %statVarAvail
  // 4 - %setEffectiveV
  // 5 - %setMaxChargeRateW
  // 6 - %setMaxDischargeRateW
  // 7 - %statWAvail
  uint32 type = 2;
}

message DERControlBase {
  optional bool opModEnergize = 1;
  optional bool opModConnect = 2;
  // for aus
  optional uint32 opModMaxLimW = 3;
  optional int64 opModExpLimW = 4;
  optional int64 opModImpLimW = 5;
  optional int64 opModGenLimW = 6;
  optional int64 opModLoadLimW = 7;

  // Low/High Voltage Ride Through
  optional Curve opModLVRTMUSTTrip = 8;
  optional Curve opModLVRTMayTrip = 9;
  optional Curve opModLVRTMomentaryCessation = 10;
  optional Curve opModHVRTMUSTTrip = 11;
  optional Curve opModHVRTMAYTrip = 12;
  optional Curve opModHVRTMomentaryCessation = 13;
  optional Curve opModLFRTMUSTTrip = 14;
  optional Curve opModLFRTMAYTrip = 15;
  optional Curve opModHFRTMUSTTrip = 16;
  optional Curve opModHFRTMAYTrip = 17;
  optional Curve opModVoltVar = 18;
  optional PowerFactor opModFixedPFInjectW = 19;
  optional PowerFactor opModFixedPFAbsorbW = 20;
  optional Curve opModVoltWatt = 21;
  optional Curve opModFreqWatt = 22;
  optional uint32 opModTargetW = 23;
  optional uint32 opModFixedW = 24;
  optional int64 opModStorageTargetW = 29; // for vpp
}

message DefaultDERControl {
  optional uint32 setGradW = 1;
  optional uint32 setSoftGradW = 2;
  DERControlBase control = 3;
  string lfdi = 4;
}

message DERControlRequest {
  string lfdi = 1;
  int64 id = 2;
  string mrid = 3;
  DERControlBase control = 4;
  string type = 5; // cancel || start || complete
  int64 start_time = 6; // 开始时间 时间戳 10位
  int64 duration = 7; // 持续时间 秒
  int64 end_time = 8; // 结束时间 时间戳 10位
}

message StepStateRequest {
  string lfdi = 1;
  int64 id = 2;
  Stage stage = 3;
  StageState state = 4;
  string message = 5; // 错误信息
  int64 timestamp = 6; // 时间戳 10位
}

enum Stage {
  StageRegisterEndDevice = 0;
  StageSetNMI = 1;
  StageReportMonitorData = 2;
  StageLoadControl = 3;
}

enum StageState {
  StageStateReady = 0;
  StageStateSuccess = 1;
  StageStateFail = 2;
}

message UtilityServerStatus {
  bool online = 1;
}