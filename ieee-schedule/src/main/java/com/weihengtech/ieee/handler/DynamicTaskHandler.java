package com.weihengtech.ieee.handler;

import com.weihengtech.ieee.dto.Task;
import com.weihengtech.ieee.interfaces.DoActionApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.util.Collection;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ScheduledFuture;

/**
 * 动态定时任务处理器
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/26 20:37
 */
@Component
@Slf4j
public class DynamicTaskHandler {

    /**
     * 任务池
     */
    public Map<String, ScheduledFuture<?>> taskMap = new ConcurrentHashMap<>();

    @Resource
    private ThreadPoolTaskScheduler taskScheduler;

    /**
     * 查看活跃任务
     *
     * @return 任务详情
     */
    public Collection<String> activeTasks() {
        log.info("thread name is: {}, pool size is: {}, active count is: {}, detail is: {}",
                taskScheduler.getThreadNamePrefix(), taskScheduler.getPoolSize(), taskScheduler.getActiveCount(),
                taskMap.keySet());
        return taskMap.keySet();
    }

    /**
     * 启动定时任务：立即开始并指定间隔持续执行
     *
     * @param task 任务
     */
    public void startFixRate(Task<? extends DoActionApi> task) {
        // 如果当前已经有这个名字的任务存在，先删除之前的，再添加现在的。（即重复就覆盖）
        boolean stopRes = stop(task.getName(), 3, "startFixRateCover");
        if (stopRes) {
            ScheduledFuture<?> scheduledFuture = taskScheduler.scheduleAtFixedRate(
                    () -> task.getService().doAction(task.getParam()), task.getPeriod());
            taskMap.put(task.getName(), scheduledFuture);
        } else {
            log.error("startFixRateCover stop {} after retry {} times still failed", task.getName(), 3);
        }
    }

    /**
     * 启动定时任务：指定时间执行一次
     *
     * @param task 任务
     */
    public void startAtTime(Task<? extends DoActionApi> task) {
        // 如果当前已经有这个名字的任务存在，先删除之前的，再添加现在的。（即重复就覆盖）
        boolean stopRes = stop(task.getName(), 3, "startAtTimeCover");
        if (stopRes) {
            ScheduledFuture<?> scheduledFuture = taskScheduler.schedule(
                    () -> task.getService().doAction(task.getParam()), task.getStartTime());
            taskMap.put(task.getName(), scheduledFuture);
        } else {
            log.error("startAtTimeCover stop {} after retry {} times still failed", task.getName(), 3);
        }

    }

    /**
     * 停止任务
     * 自动重试3次，如果最终失败则不处理
     *
     * @param name 任务名称
     */
    public void stop(String name, String sourceFlag) {
        stop(name, 3, sourceFlag);
    }

    /**
     * 停止任务并自动重试
     *
     * @param name 任务名称
     */
    public boolean stop(String name, int maxRetryTimes, String sourceFlag) {
        if (maxRetryTimes <= 0) {
            log.info("{} cancel task [{}] failed, retry {} times", sourceFlag, name, maxRetryTimes);
            taskMap.remove(name);
            return true;
        }
        if (!taskMap.containsKey(name)) {
            log.info("{} cancel task [{}] success: taskMap dosen't contains this task", sourceFlag, name);
            return true;
        }
        ScheduledFuture<?> scheduledFuture = taskMap.get(name);
        if (scheduledFuture.isCancelled()) {
            log.info("{} cancel task [{}] success: task is already cancelled", sourceFlag, name);
            return true;
        }
        if (scheduledFuture.isDone()) {
            log.info("{} cancel task [{}] success: task is already done", sourceFlag, name);
            taskMap.remove(name);
            return true;
        }
        boolean cancelRes = false;
        try {
            cancelRes = scheduledFuture.cancel(true);
        } catch (Exception e) {
            log.error(String.format("%s cancel task [%s] error: %s", sourceFlag, name, e.getMessage()), e);
        }
        if (cancelRes) {
            log.info("{} cancel task success: {}", sourceFlag, name);
            taskMap.remove(name);
            return true;
        }
        return stop(name, -- maxRetryTimes, sourceFlag);
    }
}
