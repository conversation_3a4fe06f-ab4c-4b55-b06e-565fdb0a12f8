package com.weihengtech.ieee.interfaces;

import com.weihengtech.ieee.handler.DynamicTaskHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/8/15 16:16
 */
@Component
@Slf4j
public class StopListener implements DoActionApi{

    @Resource
    private DynamicTaskHandler handler;

    @Override
    public void doAction(Object... param) {
        log.info("StopListener任务开始----------------");
        String name = (String) param[0];
        handler.stop(name, "listener");
        log.info("StopListener任务结束----------------");
    }
}
