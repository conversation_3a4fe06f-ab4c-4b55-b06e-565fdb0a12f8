package com.weihengtech.ieee.dto;

import com.weihengtech.ieee.interfaces.DoActionApi;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * task对象
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/26 20:43
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class Task<T extends DoActionApi> {

    /**
     * 任务名称
     */
    private String name;

    /**
     * 任务执行主体
     */
    private T service;

    /**
     * 执行任务参数
     */
    private Object[] param;

    /**
     * 执行间隔（只用于startFixRate）
     */
    private Long period;

    /**
     * 开始执行时间（只用于startAtTime）
     */
    private Date startTime;
}
