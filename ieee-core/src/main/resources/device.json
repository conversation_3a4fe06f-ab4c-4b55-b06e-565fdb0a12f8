{"config": {"EndDeviceReadDataDTO": [37000, 37043], "NaReadDataDTO": [37000, 37050], "SynergyReadDataDTO": [37000, 37043, 37084, 37085]}, "detail": {"EndDeviceReadDataDTO": {"deviceType": {"start": 37000, "len": 1, "slaveId": 1, "type": "U16", "alias": "The device type", "unit": "N/A", "multiply": 1.0, "prop": "deviceType"}, "activePower": {"start": 37013, "len": 1, "slaveId": 1, "type": "S16", "alias": "Grid output active power", "unit": "10W", "multiply": 0.1, "prop": "activePower"}, "reactivePower": {"start": 37014, "len": 1, "slaveId": 1, "type": "S16", "alias": "Grid output reactive power", "unit": "10Var", "multiply": 0.1, "prop": "reactivePower"}, "pcsActivePower": {"start": 37016, "len": 1, "slaveId": 1, "type": "S16", "alias": "PCS active power", "unit": "10W", "multiply": 0.1, "prop": "pcsActivePower"}, "pcsReceivePower": {"start": 37017, "len": 1, "slaveId": 1, "type": "S16", "alias": "PCS reactive power", "unit": "10Var", "multiply": 0.1, "prop": "pcsReceivePower"}, "voltageAn": {"start": 37004, "len": 1, "slaveId": 1, "type": "U16", "alias": "Grid voltage for phase R", "unit": "0.1V", "multiply": 10, "prop": "voltageAn"}, "voltageBn": {"start": 37005, "len": 1, "slaveId": 1, "type": "U16", "alias": "Grid voltage for phase S", "unit": "0.1V", "multiply": 10, "prop": "voltageBn"}, "voltageCn": {"start": 37006, "len": 1, "slaveId": 1, "type": "U16", "alias": "Grid voltage for phase T", "unit": "0.1V", "multiply": 10, "prop": "voltageCn"}, "frequency": {"start": 37007, "len": 1, "slaveId": 1, "type": "U16", "alias": "Grid frequency for phase R", "unit": "0.01Hz", "multiply": 100, "prop": "frequency"}, "setMaxPower": {"start": 37002, "len": 1, "slaveId": 1, "type": "U16", "alias": "Current dynamic maximum discharge power", "unit": "10W", "multiply": 0.1, "prop": "setMaxPower"}, "setGradW": {"start": 37026, "len": 1, "slaveId": 1, "type": "U16", "alias": "Gradient of power variation", "unit": "0.01", "multiply": 1, "prop": "setGradW"}, "connectType": {"start": 37023, "len": 1, "slaveId": 1, "type": "U16", "alias": "PCS connect status", "unit": "N/A", "multiply": 1.0, "prop": "connectType"}, "operationalMode": {"start": 37022, "len": 1, "slaveId": 1, "type": "U16", "alias": "Operational state of the DER", "unit": "W", "multiply": 1.0, "prop": "operationalMode"}, "alarm": {"start": 37019, "len": 1, "slaveId": 1, "type": "U16", "alias": "Alarm Status 1", "unit": "N/A", "multiply": 1.0, "prop": "alarm"}, "soc": {"start": 37003, "len": 1, "slaveId": 1, "type": "U16", "alias": "The current soc of battery", "unit": "N/A", "multiply": 1.0, "prop": "soc"}, "maxChargeRate": {"start": 37036, "len": 1, "slaveId": 1, "type": "S16", "alias": "Current max charge power", "unit": "10W", "multiply": 0.1, "prop": "maxChargeRate"}, "maxVa": {"start": 37037, "len": 1, "slaveId": 1, "type": "S16", "alias": "Current apparent power", "unit": "10W", "multiply": 0.1, "prop": "maxVa"}, "maxVar": {"start": 37038, "len": 1, "slaveId": 1, "type": "S16", "alias": "Current reactive power", "unit": "10Var", "multiply": 0.1, "prop": "maxVar"}, "maxVarNeg": {"start": 37039, "len": 1, "slaveId": 1, "type": "S16", "alias": "Current negative reactive power", "unit": "10Var", "multiply": 0.1, "prop": "maxVarNeg"}, "maxWh": {"start": 37042, "len": 1, "slaveId": 1, "type": "S16", "alias": "Current energy storage capacity", "unit": "10Wh", "multiply": 0.1, "prop": "maxWh"}, "inverterStatus": {"start": 37043, "len": 1, "slaveId": 1, "type": "S16", "alias": "Status of inverter", "unit": "N/A", "multiply": 1, "prop": "inverterStatus"}}, "NaReadDataDTO": {"minPfOverExcited": {"start": 37040, "len": 1, "slaveId": 1, "type": "S16", "alias": "Current power factor (overexcitation)", "unit": "N/A", "multiply": 0.01, "prop": "minPfOverExcited"}, "minPfUnderExcited": {"start": 37041, "len": 1, "slaveId": 1, "type": "S16", "alias": "Current power factor (under excited)", "unit": "N/A", "multiply": 0.01, "prop": "minPfUnderExcited"}, "availabilityDuration": {"start": 37045, "len": 1, "slaveId": 1, "type": "S16", "alias": "The available duration of DER represents the time during which a device can operate normally under specific conditions", "unit": "0.1H", "multiply": 10, "prop": "availabilityDuration"}, "maxChargeDuration": {"start": 37046, "len": 1, "slaveId": 1, "type": "S16", "alias": "The maximum charging time refers to the longest charging time of the device in charging mode", "unit": "0.1H", "multiply": 10, "prop": "maxChargeDuration"}, "reserveChargePercent": {"start": 37047, "len": 1, "slaveId": 1, "type": "S16", "alias": "Backup charging percentage, indicating the percentage of charging capacity reserved by the device to cope with sudden demands or backup power needs", "unit": "%", "multiply": 1, "prop": "reserveChargePercent"}, "reservePercent": {"start": 37048, "len": 1, "slaveId": 1, "type": "S16", "alias": "Backup percentage refers to the percentage of total power reserved by equipment to cope with unexpected situations (such as failures)", "unit": "%", "multiply": 1, "prop": "reservePercent"}, "statVarAvail": {"start": 37049, "len": 1, "slaveId": 1, "type": "S16", "alias": "Currently available reactive power", "unit": "10Var", "multiply": 1, "prop": "statVarAvail"}, "statWAvail": {"start": 37049, "len": 1, "slaveId": 1, "type": "S16", "alias": "Currently available active power", "unit": "10W", "multiply": 1, "prop": "statWAvail"}}, "SynergyReadDataDTO": {"setMinWh": {"start": 37084, "len": 1, "slaveId": 1, "type": "U16", "alias": "Real time data, representing the value at which thebattery stops discharging", "unit": "10Wh", "multiply": 0.1, "prop": "setMinWh"}, "storedEnergy": {"start": 37085, "len": 1, "slaveId": 1, "type": "U16", "alias": "Real time data, available energy refers to the energythat can be discharged before the system reaches thereserved minimum power level", "unit": "10Wh", "multiply": 0.1, "prop": "storedEnergy"}}}}