package com.weihengtech.ieee.utils;

import lombok.AllArgsConstructor;

import jakarta.xml.bind.DatatypeConverter;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;

/**
 *
 * <AUTHOR>
 * @date 2024/7/25 15:51
 * @version 1.0
 */
public class SecurityUtil {

    @AllArgsConstructor
    public static class DeviceFDI {
        public String lfdi;
        public Long sfdi;
    }

    public static int checkDigit(long x) {
        int sum = 0;
        while (x > 0) {
            sum += (int) (x % 10);
            x /= 10;
        }
        return (10 - (sum % 10)) % 10;
    }

    public static long sfdiGen(byte[] lfdi) {
        long sfdi = 0;
        for (int i = 0; i < 5; i++) {
            sfdi = (sfdi << 8) + (lfdi[i] & 0xff);
        }
        sfdi >>= 4;
        sfdi = sfdi * 10 + checkDigit(sfdi);
        return sfdi;
    }

    public static DeviceFDI loadDeviceX509(String path) throws IOException, NoSuchAlgorithmException {
        if (!path.endsWith(".x509")) {
            throw new IllegalArgumentException("Invalid file extension");
        }

        Path filePath = Paths.get(path);
        byte[] buffer = Files.readAllBytes(filePath);

        MessageDigest digest = MessageDigest.getInstance("SHA-256");
        byte[] hashed = digest.digest(buffer);
        String lfdi = DatatypeConverter.printHexBinary(hashed).substring(0, 40).toLowerCase();
        long sfdi = sfdiGen(hashed);

        return new DeviceFDI(lfdi, sfdi);
    }

    public static DeviceFDI randomFdi() {
        // 创建一个SecureRandom实例
        SecureRandom secureRandom = new SecureRandom();
        // 创建一个用于保存随机数的byte数组
        byte[] hashed = new byte[32];
        // 生成随机数
        secureRandom.nextBytes(hashed);
        String lfdi = DatatypeConverter.printHexBinary(hashed).substring(0, 40).toLowerCase();
        long sfdi = sfdiGen(hashed);
        return new DeviceFDI(lfdi, sfdi);
    }
}

