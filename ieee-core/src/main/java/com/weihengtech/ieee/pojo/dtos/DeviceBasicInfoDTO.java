package com.weihengtech.ieee.pojo.dtos;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DeviceBasicInfoDTO implements Serializable {

	private static final long serialVersionUID = 1L;

	private Long activeTime;

	private String category;

	private String categoryName;

	private Long createTime;

	private String gatewayId;

	private String icon;

	private String id;

	private String ip;

	private String lat;

	private String localKey;

	private String lon;

	private String model;

	private String name;

	private Boolean online;

	private String ownerId;

	private String productId;

	private String productName;

	private Boolean sub;

	private String timeZone;

	private Long updateTime;

	private String uuid;
}
