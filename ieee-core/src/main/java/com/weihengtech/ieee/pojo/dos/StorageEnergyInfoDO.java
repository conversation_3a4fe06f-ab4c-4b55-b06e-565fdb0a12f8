package com.weihengtech.ieee.pojo.dos;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.weihengtech.ieee.EndDeviceProto;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <p>
 * 设备扩展信息
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-02
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@TableName("storage_energy_info")
public class StorageEnergyInfoDO {


    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 设备id
     */
    private Long deviceId;

    /**
     * 设备类型
     */
    private String deviceType;

    /**
     * 长id
     */
    private String lfdi;

    /**
     * 短id
     */
    private Long sfdi;

    /**
     * pin值
     */
    private Integer pin;

    /**
     * 设计最大功率
     */
    private Integer designMaxPower;

    /**
     * 国家
     */
    private Integer country;

    /**
     * 电网公司
     */
    private Integer gridCompany;

    /**
     * 电网公司(lfdi固定值)
     */
    private String gridCompanyCode;

    /**
     * 户号
     */
    private String nmi;

    /**
     * 北美机：Designed max charge power
     */
    private Integer maxChargeRate;

    private Integer maxDischargeRate;

    /**
     * 北美机：Designed max apparent power
     */
    private Integer maxVa;

    /**
     * 北美机：Designed max reactive power
     */
    private Integer maxVar;

    /**
     * 北美机：Designed max negative reactive power
     */
    private Integer maxVarNeg;

    /**
     * 北美机：Designed min power factor (over excitation)
     */
    private Integer minPfOverExcited;

    /**
     * 北美机：Designed min power factor (under excited)
     */
    private Integer minPfUnderExcited;

    /**
     * 北美机：Designed max energy storage capacity
     */
    private Integer maxWh;

    private Date crtTime;

    private String crtBy;


    /**
     * 构建设备控制信息
     *
     * @param deviceList 设备原始数据列表
     * @return 设备控制信息
     */
    public static List<EndDeviceProto.EndDevice> buildEndDeviceList(List<StorageEnergyInfoDO> deviceList) {
        return deviceList.stream()
                .map(i -> EndDeviceProto.EndDevice.newBuilder()
                        .setId(i.getDeviceId())
                        .setLfdi(i.getLfdi())
                        .setSfdi(i.getSfdi())
//                        .setPin(i.getPin())
                        .setNmi(i.getNmi())
                        // 单位是10W，需要转换为W
                        .setDesignMaxPower(i.getDesignMaxPower() * 10)
                        // 默认Combined py and storage 23
                        .setDeviceCategory(23)
                        .setDerType(EndDeviceProto.DERType.DERTypeCombinedPVAndStorage)
                        // 固定值
                        .setControlMode(EndDeviceProto.DERControlMode.newBuilder()
                                .setChargeMode(true)
                                .setDischargeMode(true)
                                .setOpModEnergize(true)
                                .setOpModMaxLimW(true)
                                .build())
                        // 固定值
                        .setDoeControlMode(EndDeviceProto.DoeDERControlMode.newBuilder()
                                .setOpModExpLimW(true)
                                .setOpModGenLimW(true)
                                .build())
                        .setRtgMaxChargeRateW(Optional.ofNullable(i.getMaxChargeRate()).map(k -> k * 10).orElse(0))
                        .setRtgMaxDischargeRateW(Optional.ofNullable(i.getMaxDischargeRate()).map(k -> k * 10).orElse(0))
                        .setRtgMaxVA(Optional.ofNullable(i.getMaxVa()).map(k -> k * 10).orElse(0))
                        .setRtgMaxVar(Optional.ofNullable(i.getMaxVar()).map(k -> k * 10).orElse(0))
                        .setRtgMaxVarNeg(Optional.ofNullable(i.getMaxVarNeg()).map(k -> k * 10).orElse(0))
                        .setRtgMinPFOverExcited(Optional.ofNullable(i.getMinPfOverExcited())/*.map(k -> k / 100)*/.orElse(0))
                        .setRtgMinPFUnderExcited(Optional.ofNullable(i.getMinPfUnderExcited())/*.map(k -> k / 100)*/.orElse(0))
                        .setRtgMaxWh(Optional.ofNullable(i.getMaxWh()).map(k -> k * 10).orElse(0))
                        .setVppControlMode(EndDeviceProto.VPPDERControlMode.newBuilder()
                                .setOpModStorageTargetW(true)
                                .build())
                        .build())
                .collect(Collectors.toList());
    }
}
