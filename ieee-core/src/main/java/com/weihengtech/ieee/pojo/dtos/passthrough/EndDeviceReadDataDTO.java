package com.weihengtech.ieee.pojo.dtos.passthrough;

import com.weihengtech.ieee.EndDeviceProto;
import com.weihengtech.ieee.pojo.dos.StorageEnergyInfoDO;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/8/5 15:10
 */
@Data
public class EndDeviceReadDataDTO {
    private Integer deviceType;
    private Double activePower;
    private Double reactivePower;
    private Double pcsActivePower;
    private Double pcsReceivePower;
    private Double voltageAn;
    private Double voltageBn;
    private Double voltageCn;
    private Double frequency;
    private Integer setMaxPower;
    private Integer setGradW;
    private Integer connectType;
    private Integer operationalMode;
    private Integer alarm;
    private float soc;
    private Integer maxVa;
    private Integer maxVar;
    private Integer maxVarNeg;
    private Integer maxWh;
    private Integer maxChargeRate;
    private Integer inverterStatus;

    /** 透传结果转换设备数据 */
    public EndDeviceProto.EndDeviceData buildEndDeviceData(StorageEnergyInfoDO endDevice) {
        return EndDeviceProto.EndDeviceData.newBuilder()
                .setLfdi(endDevice.getLfdi())
                .setActivePower(this.getActivePower())
                .setReactivePower(this.getReactivePower())
                .setPcsActivePower(this.getPcsActivePower())
                .setPcsReactivePower(this.getPcsReceivePower())
                .setVoltageAn(this.getVoltageAn())
                .setVoltageBn(this.getVoltageBn())
                .setVoltageCn(this.getVoltageCn())
                .setFrequency(this.getFrequency())
                .setSetMaxPower(this.getSetMaxPower())
                .setSetGradW(this.getSetGradW())
                .setDoeControlMode(buildControlMode())
                .setGenerator(parseConnectType(this.getConnectType()))
//                .setStor(parseConnectType(res.getConnectType()))
                .setOperationalMode(this.getOperationalMode())
                .setAlarm(parseAlarm(this.getAlarm()))
                .setSoc(this.getSoc())
                .setDeviceType(EndDeviceProto.DeviceType.forNumber(this.getDeviceType()))
                .setSetMaxVA(this.getMaxVa())
                .setSetMaxVar(this.getMaxVar())
                .setSetMaxVarNeg(this.getMaxVarNeg())
                .setSetMaxWh(this.getMaxWh())
                .setSetMaxChargeRateW(this.getMaxChargeRate())
                .setSetMaxDischargeRateW(this.getSetMaxPower())
                .setInverterStatus(this.getInverterStatus())
                .build();
    }

    /** 解析连接类型 */
    private static EndDeviceProto.ConnectType parseConnectType(Integer val) {
        EndDeviceProto.ConnectType.Builder builder = EndDeviceProto.ConnectType.newBuilder();
        if ((val & 0x01) != 0) {
            builder.setConnected(true);
        }
        if ((val & 0x02) != 0) {
            builder.setAvailable(true);
        }
        if ((val & 0x04) != 0) {
            builder.setOperating(true);
        }
        if ((val & 0x08) != 0) {
            builder.setTest(true);
        }
        if ((val & 0x10) != 0) {
            builder.setFault(true);
        }
        return builder.build();
    }

    /** 构建模式模式 */
    private static EndDeviceProto.DoeDERControlMode buildControlMode() {
        return EndDeviceProto.DoeDERControlMode.newBuilder()
                .setOpModExpLimW(true)
                .setOpModGenLimW(true)
                .build();
    }

    /** 解析告警类型 */
    private static EndDeviceProto.Alarm parseAlarm(Integer val) {
        EndDeviceProto.Alarm.Builder builder = EndDeviceProto.Alarm.newBuilder();
        if ((val & 0x01) != 0) {
            builder.setOverCurrent(true);
        }
        if ((val & 0x02) != 0) {
            builder.setOverVoltage(true);
        }
        if ((val & 0x04) != 0) {
            builder.setUnderVoltage(true);
        }
        if ((val & 0x08) != 0) {
            builder.setOverFrequency(true);
        }
        if ((val & 0x10) != 0) {
            builder.setUnderFrequency(true);
        }
        return builder.build();
    }
}
