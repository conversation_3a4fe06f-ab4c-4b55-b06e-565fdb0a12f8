package com.weihengtech.ieee.pojo.dtos.passthrough;

import com.weihengtech.ieee.EndDeviceProto;
import com.weihengtech.ieee.pojo.dos.StorageEnergyInfoDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/8/5 15:10
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SynergyReadDataDTO extends EndDeviceReadDataDTO{

    private Integer setMinWh;

    private Integer storedEnergy;

    /** 透传结果转换设备数据 */
    @Override
    public EndDeviceProto.EndDeviceData buildEndDeviceData(StorageEnergyInfoDO endDevice) {
        EndDeviceProto.EndDeviceData endDeviceData = super.buildEndDeviceData(endDevice);
        return endDeviceData.toBuilder()
                .setVppControlMode(EndDeviceProto.VPPDERControlMode.newBuilder()
                        .setOpModStorageTargetW(true)
                        .build())
                .setSetMinWh(this.getSetMinWh())
                .setStoredEnergy(this.getStoredEnergy())
                .build();
    }
}
