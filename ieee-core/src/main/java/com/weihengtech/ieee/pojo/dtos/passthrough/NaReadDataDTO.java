package com.weihengtech.ieee.pojo.dtos.passthrough;

import com.weihengtech.ieee.EndDeviceProto;
import com.weihengtech.ieee.pojo.dos.StorageEnergyInfoDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/8/5 15:10
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class NaReadDataDTO extends EndDeviceReadDataDTO{

    private Integer minPfOverExcited;
    private Integer minPfUnderExcited;


    private Integer availabilityDuration;
    private Integer maxChargeDuration;
    private Integer reserveChargePercent;
    private Integer reservePercent;
    private Integer statVarAvail;
    private Integer statWAvail;

    /** 透传结果转换设备数据 */
    @Override
    public EndDeviceProto.EndDeviceData buildEndDeviceData(StorageEnergyInfoDO endDevice) {
        EndDeviceProto.EndDeviceData endDeviceData = super.buildEndDeviceData(endDevice);
        return endDeviceData.toBuilder()
                .setSetMinPFOverExcited(this.getMinPfOverExcited())
                .setSetMinPFUnderExcited(this.getMinPfUnderExcited())
                .setAvailabilityDuration(this.getAvailabilityDuration())
                .setMaxChargeDuration(this.getMaxChargeDuration())
                .setReserveChargePercent(this.getReserveChargePercent())
                .setReservePercent(this.getReservePercent())
                .setStatVarAvail(this.getStatVarAvail())
                .setStatWAvail(this.getStatWAvail())
                .build();
    }
}
