package com.weihengtech.ieee.pojo.dos;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 设备信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-24
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("hybrid_single_phase")
public class DeviceListDO implements Serializable {

    private static final long serialVersionUID=1L;

    /**
     * 设备列表主键id
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 别名
     */
    private String alias;

    /**
     * 设备名
     */
    private String deviceName;

    /**
     * wifi sn
     */
    private String wifiSn;

    /**
     * 设备序列号
     */
    private String deviceSn;

    /**
     * -5 未知, -4: 设备已禁用; -3: 设备未激活; -1 设备离线 0: 等待 1: 并网 2: EPS 3: 故障; 4:保留 5: 自检
     */
    private Integer state;

    /**
     * 0: 易联 1: 涂鸦
     */
    private Integer dataSource;

    /**
     * 0: 易联tsdb 1: tuya lindorm 2 易联 lindorm
     */
    private Integer tsdbSource;

    /**
     * 经度
     */
    private BigDecimal longitude;

    /**
     * 纬度
     */
    private BigDecimal latitude;

    /**
     * 0: 关闭; 1: 开启
     */
    private Boolean vppMode;

    /**
     * 设备型号
     */
    private String deviceModel;

    /**
     * 品牌
     */
    private String brand;

    /**
     * 生产商
     */
    private String factory;

    /**
     * 功率板版本号
     */
    private String powerBoardHardwareVersion;

    /**
     * dsp1软件版本号
     */
    private String dsp1SoftwareVersion;

    private String dsp1SubVersion;

    /**
     * dsp2软件版本号
     */
    private String dsp2SoftwareVersion;

    private String dsp2SubVersion;

    /**
     * ems软件版本号
     */
    private String emsSoftwareVersion;

    /**
     * ems硬件版本号
     */
    private String emsHardwareVersion;

    private String emsSubVersion;

    /**
     * wifi硬件版本号
     */
    private String wifiHardwareVersion;

    /**
     * wifi棒版本号
     */
    private String wifiSoftwareVersion;

    /**
     * 电量计版本号
     */
    private String bmsGaugeVersion;

    /**
     * bms序列号
     */
    private String bmsSn;

    /**
     * bms厂家
     */
    private String bmsVendor;

    /**
     * bms软件版本号
     */
    private String bmsSoftwareVersion;

    /**
     * bms硬件版本号
     */
    private String bmsHardwareVersion;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 首次安装时间
     */
    private Long firstInstall;

    private Integer countryId;

    /**
     * 数据中心id
     */
    private Integer datacenterId;

    /**
     * 安装IP地址
     */
    private String ip;

    /**
     * 品类
     */
    private String category;

    /**
     * 型号
     */
    private String model;

    private String arcDspSoftwareVersion;
    private String arcDspSubVersion;
    private String arcDspBootLoaderSoftwareVersion;


}
