package com.weihengtech.ieee.delay;

import com.weihengtech.ieee.dto.Task;
import com.weihengtech.ieee.interfaces.DoActionApi;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.concurrent.Delayed;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2025/6/25 18:56
 * @version 1.0
 */
@Getter
@Setter
@ToString
public class DelayQueueMessage implements Delayed {

	/**
	 * 延迟时间
	 */
	private long delayTime;

	/**
	 * 执行任务信息
	 */
	private Task<? extends DoActionApi> task;

	public DelayQueueMessage(long delayTime) {
		this.delayTime = delayTime;
	}

	@Override
	public long getDelay(TimeUnit unit) {
		return unit.convert(this.delayTime - System.currentTimeMillis(), TimeUnit.MILLISECONDS);
	}

	@Override
	public int compareTo(Delayed o) {
		long thisDelay = this.getDelay(TimeUnit.MILLISECONDS);
		long otherDelay = o.getDelay(TimeUnit.MILLISECONDS);
		return Long.compare(thisDelay, otherDelay);
	}
}
