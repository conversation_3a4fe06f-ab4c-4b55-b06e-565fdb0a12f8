package com.weihengtech.ieee.delay;

import com.weihengtech.ieee.constants.Constants;
import com.weihengtech.ieee.dto.Task;
import com.weihengtech.ieee.interfaces.DoActionApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025/6/25 17:32
 * @version 1.0
 */
@Slf4j
@Service
public class MqServiceImpl implements MqService {

	@Resource
	private RedisTemplate<String, Object> redisTemplate;


	@Override
	public void doAction(DelayQueueMessage message) {
		Task<? extends DoActionApi> task = message.getTask();
		if (!redisTemplate.hasKey(buildTaskKey(task.getName()))) {
			log.info("{}执行周期结束", task.getName());
			return;
		}
        try {
            task.getService().doAction(task.getParam());
        } catch (Exception e) {
            // no nothing
        }
        // 推送下发指令任务
		DelayQueueMessage postMessage = new DelayQueueMessage(System.currentTimeMillis() + task.getPeriod() * 1000);
		postMessage.setTask(task);
		InitUtil.DELAY_QUEUE.offer(postMessage);
	}

	@Override
	public void startTask(Task<? extends DoActionApi> task) {
		log.info("开始推送周期执行任务：{}", task.getName());
		long now = System.currentTimeMillis();
		// 推送下发指令任务
		DelayQueueMessage postMessage = new DelayQueueMessage(now + task.getPeriod() * 1000);
		postMessage.setTask(task);
		InitUtil.DELAY_QUEUE.offer(postMessage);
	}

	private String buildTaskKey(String taskName) {
		return String.format(Constants.TASK_KEY, taskName);
	}
}
