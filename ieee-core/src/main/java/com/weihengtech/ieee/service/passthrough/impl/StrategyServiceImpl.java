package com.weihengtech.ieee.service.passthrough.impl;

import com.weihengtech.ieee.enums.GridCompanyEnum;
import com.weihengtech.ieee.pojo.dos.DeviceListDO;
import com.weihengtech.ieee.pojo.dtos.passthrough.EndDeviceReadDataDTO;
import com.weihengtech.ieee.pojo.dtos.passthrough.NaReadDataDTO;
import com.weihengtech.ieee.pojo.dtos.passthrough.SynergyReadDataDTO;
import com.weihengtech.ieee.service.device.DeviceListService;
import com.weihengtech.ieee.service.passthrough.SpecificServService;
import com.weihengtech.ieee.service.passthrough.StrategyService;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import java.util.HashMap;
import java.util.Map;


/**
 * <AUTHOR>
 */
@Service
public class StrategyServiceImpl implements StrategyService, ApplicationContextAware {

	private ApplicationContext applicationContext;

	@Resource
	private DeviceListService deviceListService;

	private final Map<String, Class<? extends EndDeviceReadDataDTO>> readDataMap = new HashMap<>();

	@PostConstruct
	public void init() {
		readDataMap.put("103", NaReadDataDTO.class);
		readDataMap.put("101", EndDeviceReadDataDTO.class);
		readDataMap.put("102", EndDeviceReadDataDTO.class);
	}

	@Override
	public SpecificServService chooseSpecificServ(String deviceId) {
		DeviceListDO deviceInfo = deviceListService.getById(deviceId);
		return chooseSpecificServ(deviceInfo);
	}

	@Override
	public SpecificServService chooseSpecificServ(DeviceListDO deviceInfo) {
		return applicationContext.getBean("specificServ" + deviceInfo.getDataSource(), SpecificServService.class);
	}

    @Override
    public EndDeviceReadDataDTO chooseReadData(Integer company, String deviceType) {
		Class<? extends EndDeviceReadDataDTO> clazz;
		if (GridCompanyEnum.Synergy.getId() == company) {
			clazz = SynergyReadDataDTO.class;
		} else {
			clazz = readDataMap.get(deviceType);
		}
        try {
            return clazz.newInstance();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
	public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
		this.applicationContext = applicationContext;
	}
}
