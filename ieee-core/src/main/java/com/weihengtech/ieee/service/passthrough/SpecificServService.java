package com.weihengtech.ieee.service.passthrough;

import com.weihengtech.ieee.pojo.vos.ReadBatchDeviceVO;
import com.weihengtech.ieee.pojo.vos.ReadDeviceVO;
import com.weihengtech.ieee.pojo.vos.WriteBatchDeviceVO;
import com.weihengtech.ieee.pojo.vos.WriteDeviceVO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface SpecificServService {

	/**
	 * 发送透传读命令
	 *
	 * @param param param
     * @return 读取数据
	 */
	List<Integer> sendReadCommand(ReadDeviceVO param);

	/**
	 * 发送批量透传读命令
	 *
	 * @param param param
	 * @return 读取数据
	 */
	Map<String, List<Integer>> sendBatchReadCommand(ReadBatchDeviceVO param);

	/**
	 * 发送透传写命令
	 *
	 * @param param param
     * @return 写入结果
	 */
	Boolean sendWriteCommand(WriteDeviceVO param);

	/**
	 * 发送透传写命令(异步，实时响应返回)
	 *
	 * @param param
	 * @return
	 */
	Boolean sendWriteCommandAsync(WriteDeviceVO param);

	/**
	 * 发送批量透传写命令
	 *
	 * @param param param
	 * @return 写入结果
	 */
	Boolean sendBatchWriteCommand(WriteBatchDeviceVO param);
}
