package com.weihengtech.ieee.service.device.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.weihengtech.ieee.pojo.dos.StorageEnergyInfoDO;
import com.weihengtech.ieee.dao.StorageEnergyInfoMapper;
import com.weihengtech.ieee.service.device.StorageEnergyInfoService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 设备扩展信息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-02
 */
@Service
public class StorageEnergyInfoServiceImpl extends ServiceImpl<StorageEnergyInfoMapper, StorageEnergyInfoDO> implements StorageEnergyInfoService {

    @Override
    public StorageEnergyInfoDO getByDeviceId(Long deviceId) {
        LambdaQueryWrapper<StorageEnergyInfoDO> wrapper = Wrappers.<StorageEnergyInfoDO>lambdaQuery()
                .eq(StorageEnergyInfoDO::getDeviceId, deviceId);
        return getOne(wrapper);
    }

    @Override
    public StorageEnergyInfoDO getByLfdi(String lfdi) {
        LambdaQueryWrapper<StorageEnergyInfoDO> wrapper = Wrappers.<StorageEnergyInfoDO>lambdaQuery()
                .eq(StorageEnergyInfoDO::getLfdi, lfdi);
        return getOne(wrapper);
    }
}
