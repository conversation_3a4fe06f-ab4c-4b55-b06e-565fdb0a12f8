package com.weihengtech.ieee.service.device;


import com.baomidou.mybatisplus.extension.service.IService;
import com.weihengtech.ieee.EndDeviceProto;
import com.weihengtech.ieee.pojo.dos.StorageDynamicStageDO;

/**
 * <p>
 * 设备动态输出校验阶段 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-28
 */
public interface StorageDynamicStageService extends IService<StorageDynamicStageDO> {

    StorageDynamicStageDO getByExportId(Integer exportId, Integer stage);

    void updateStepState(EndDeviceProto.StepStateRequest request);
}
