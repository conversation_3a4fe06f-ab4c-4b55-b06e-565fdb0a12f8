package com.weihengtech.ieee.service.modbus;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Pair;
import com.weihengtech.ieee.EndDeviceProto;
import com.weihengtech.ieee.pojo.vos.WriteDeviceVO;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/11/13 15:57
 */
public class NaWriteMapUtil {

    private static final Map<String, Pair<Integer, Integer>> METRIC_MAP = new HashMap<>();
    private static final Map<String, Pair<Integer, Integer>> METRIC_DEF_MAP = new HashMap<>();

    static {
        // 控制点位映射
        METRIC_MAP.put("LVRT", Pair.of(61301, 61307));
        METRIC_MAP.put("HVRT", Pair.of(61308, 61314));
        METRIC_MAP.put("LFRT", Pair.of(61315, 61321));
        METRIC_MAP.put("HFRT", Pair.of(61322, 61328));

        // 默认点位映射
        METRIC_DEF_MAP.put("LVRT", Pair.of(61601, 61607));
        METRIC_DEF_MAP.put("HVRT", Pair.of(61608, 61614));
        METRIC_DEF_MAP.put("LFRT", Pair.of(61615, 61621));
        METRIC_DEF_MAP.put("HFRT", Pair.of(61622, 61628));
    }

    /** 构建OpModTrip曲线 */
    public static WriteDeviceVO buildOpModTrip(EndDeviceProto.DERControlBase control, boolean isDefault) {
        List<Integer> resValList = new ArrayList<>(28);
        EndDeviceProto.Curve lvrt = findExistsOne(control.getOpModLVRTMUSTTrip(), control.getOpModLVRTMayTrip(), control.getOpModLVRTMomentaryCessation());
        EndDeviceProto.Curve hvrt = findExistsOne(control.getOpModHVRTMUSTTrip(), control.getOpModHVRTMAYTrip(), control.getOpModHVRTMomentaryCessation());
        EndDeviceProto.Curve lfrt = findExistsOne(control.getOpModLFRTMUSTTrip(), control.getOpModLFRTMAYTrip());
        EndDeviceProto.Curve hfrt = findExistsOne(control.getOpModHFRTMUSTTrip(), control.getOpModHFRTMAYTrip());
        resValList.addAll(buildSimpleCurve("LVRT", lvrt, 1f, isDefault));
        resValList.addAll(buildSimpleCurve("HVRT", hvrt, 1f, isDefault));
        resValList.addAll(buildSimpleCurve("LFRT", lfrt, 100f, isDefault));
        resValList.addAll(buildSimpleCurve("HFRT", hfrt, 100f, isDefault));
        return WriteDeviceVO.builder()
                .slaveId(1)
                .startAddress(isDefault ? 61601 : 61301)
                .len(28)
                .values(resValList)
                .build();
    }

    /** 构建OpModTrip各项曲线 */
    private static List<Integer> buildSimpleCurve(String paramName, EndDeviceProto.Curve param, float multiply, boolean isDefault) {
        List<EndDeviceProto.Point> pointsList = param.getPointsList();
        Pair<Integer, Integer> metricPair = isDefault ? METRIC_DEF_MAP.get(paramName) : METRIC_MAP.get(paramName);
        List<Integer> values = new ArrayList<>(Collections.nCopies(metricPair.getValue() - metricPair.getKey() + 1, 0));
        if (CollUtil.isEmpty(param.getPointsList())) {
            return values;
        }
        values.set(0, 1);
        int mid = (metricPair.getValue() - metricPair.getKey()) / 2;
        for (int i = 1; i <= mid; i++) {
            if (i >= pointsList.size()) {
                continue;
            }
            values.set(i, ModbusParseUtil.encodeU16(pointsList.get(i - 1).getY(), multiply));
            values.set(i + mid, ModbusParseUtil.encodeU16(pointsList.get(i - 1).getX(), 1000f));
        }
        return values;
    }

    /** 互斥值中找出不为空的值 */
    @SafeVarargs
    public static <T> T findExistsOne(T... params) {
        return Arrays.stream(params).filter(Objects::nonNull).findFirst().orElse(null);
    }

    /** 构建opModVoltVar曲线 */
    public static WriteDeviceVO buildOpModVoltVar(EndDeviceProto.Curve opModVoltVar, boolean isDefault) {
        int startAddress = isDefault ? 61802 : 61505;
        // 没有坐标值，默认disabled
        if (CollUtil.isEmpty(opModVoltVar.getPointsList())) {
            return WriteDeviceVO.builder()
                    .slaveId(1)
                    .startAddress(startAddress + 1)
                    .len(1)
                    .values(Collections.singletonList(0))
                    .build();
        }
        int mid = 4;
        List<Integer> resValList = new ArrayList<>(Collections.nCopies(10, 0));
        // yRefType类型
        resValList.set(0, transOpModVoltVarRefType(opModVoltVar.getYRefType()));
        // 使能开关
        resValList.set(1, 1);
        // 曲线坐标
        List<EndDeviceProto.Point> pointsList = opModVoltVar.getPointsList();
        for (int i = 1; i <= mid; i++) {
            if (i > pointsList.size()) {
                continue;
            }
            resValList.set(i, ModbusParseUtil.encodeU16(pointsList.get(i - 1).getX(), 1f));
            resValList.set(i + mid, ModbusParseUtil.encodeS16(pointsList.get(i - 1).getY(), 1f));
        }
        // 设置时间斜率
        resValList.add(opModVoltVar.getOpenLoopTms());
        resValList.add(opModVoltVar.getOpenLoopTms());
        return WriteDeviceVO.builder()
                .slaveId(1)
                .startAddress(startAddress)
                .len(12)
                .values(resValList)
                .build();
    }

    /** 构建opModVoltWatt曲线 */
    public static List<WriteDeviceVO> buildOpModVoltWatt(EndDeviceProto.Curve opModVoltWatt, boolean isDefault) {
        // 没有坐标值，默认欠压过压都需要disabled
        if (CollUtil.isEmpty(opModVoltWatt.getPointsList())) {
            return Arrays.asList(WriteDeviceVO.builder()
                    .slaveId(1)
                    .startAddress(isDefault ? 61702 : 61402)
                    .len(1)
                    .values(Collections.singletonList(0))
                    .build(),
                    WriteDeviceVO.builder()
                            .slaveId(1)
                            .startAddress(isDefault ? 61713 : 61413)
                            .len(1)
                            .values(Collections.singletonList(0))
                            .build());
        }
        List<WriteDeviceVO> resList = new ArrayList<>(3);
        // yRefType类型
        resList.add(WriteDeviceVO.builder()
                .slaveId(1)
                .startAddress(isDefault ? 61701 : 61401)
                .len(1)
                .values(Collections.singletonList(transOpModVoltWattRefType(opModVoltWatt.getYRefType())))
                .build());
        // 根据默认、控制、过压、欠压确定点位
        boolean over = opModVoltWatt.getPointsList().stream()
                .map(EndDeviceProto.Point::getX)
                .allMatch(i -> i >= 100);
        boolean under = opModVoltWatt.getPointsList().stream()
                .map(EndDeviceProto.Point::getX)
                .allMatch(i -> i <= 100);
        int startAddress;
        int mid = 4;
        int disabledAddress;
        if (isDefault && over) {
            startAddress = 61702;
            disabledAddress = 61713;
        } else if (isDefault && under) {
            startAddress = 61713;
            disabledAddress = 61702;
        } else if (!isDefault && over) {
            startAddress = 61402;
            disabledAddress = 61413;
        } else {
            startAddress = 61413;
            disabledAddress = 61402;
        }
        // 禁用另一个使能开关
        resList.add(WriteDeviceVO.builder()
                .slaveId(1)
                .startAddress(disabledAddress)
                .len(1)
                .values(Collections.singletonList(0))
                .build());
        // 构建曲线点位值
        List<Integer> resValList = new ArrayList<>(Collections.nCopies(9, 0));
        // 使能开关
        resValList.set(0, 1);
        // 曲线坐标
        List<EndDeviceProto.Point> pointsList = opModVoltWatt.getPointsList();
        for (int i = 1; i <= mid; i++) {
            if (i > pointsList.size()) {
                continue;
            }
            resValList.set(i, ModbusParseUtil.encodeU16(pointsList.get(i - 1).getX(), 1f));
            resValList.set(i + mid, ModbusParseUtil.encodeU16(pointsList.get(i - 1).getY(), 1f));
        }
        // 时间斜率
        resValList.add(opModVoltWatt.getOpenLoopTms());
        resValList.add(opModVoltWatt.getOpenLoopTms());
        resList.add(WriteDeviceVO.builder()
                .slaveId(1)
                .startAddress(startAddress)
                .len(11)
                .values(resValList)
                .build());
        return resList;
    }

    /** 构建opModFreqWatt曲线 */
    public static List<WriteDeviceVO> buildOpModFreqWatt(EndDeviceProto.Curve opModFreqWatt, boolean isDefault) {
        // 没有坐标值，默认欠压过压都需要disabled
        if (CollUtil.isEmpty(opModFreqWatt.getPointsList())) {
            return Arrays.asList(WriteDeviceVO.builder()
                            .slaveId(1)
                            .startAddress(isDefault ? 61724 : 61424)
                            .len(1)
                            .values(Collections.singletonList(0))
                            .build(),
                    WriteDeviceVO.builder()
                            .slaveId(1)
                            .startAddress(isDefault ? 61734 : 61434)
                            .len(1)
                            .values(Collections.singletonList(0))
                            .build());
        }
        List<WriteDeviceVO> resList = new ArrayList<>(2);
        // 根据默认、控制、过频、欠频确定点位
        boolean over = opModFreqWatt.getPointsList().stream()
                .map(EndDeviceProto.Point::getX)
                .allMatch(i -> i >= 60);
        boolean under = opModFreqWatt.getPointsList().stream()
                .map(EndDeviceProto.Point::getX)
                .allMatch(i -> i <= 60);
        int startAddress;
        int mid = 3;
        int disabledAddress;
        if (isDefault && over) {
            startAddress = 61724;
            disabledAddress = 61734;
        } else if (isDefault && under) {
            startAddress = 61734;
            disabledAddress = 61724;
        } else if (!isDefault && over) {
            startAddress = 61424;
            disabledAddress = 61434;
        } else {
            startAddress = 61434;
            disabledAddress = 61424;
        }
        // 禁用另一个使能开关
        resList.add(WriteDeviceVO.builder()
                .slaveId(1)
                .startAddress(disabledAddress)
                .len(1)
                .values(Collections.singletonList(0))
                .build());
        // 构建曲线点位值
        List<Integer> resValList = new ArrayList<>(Collections.nCopies(7, 0));
        // 使能开关
        resValList.set(0, 1);
        // 曲线坐标
        List<EndDeviceProto.Point> pointsList = opModFreqWatt.getPointsList();
        for (int i = 1; i <= mid; i++) {
            if (i > pointsList.size()) {
                continue;
            }
            resValList.set(i, ModbusParseUtil.encodeU16(pointsList.get(i - 1).getX(), 100f));
            resValList.set(i + mid, ModbusParseUtil.encodeU16(pointsList.get(i - 1).getY(), 1f));
        }
        // 时间斜率
        resValList.add(opModFreqWatt.getRampDecTms());
        resValList.add(opModFreqWatt.getRampIncTms());
        resValList.add(opModFreqWatt.getRampPT1Tms());
        resList.add(WriteDeviceVO.builder()
                .slaveId(1)
                .startAddress(startAddress)
                .len(10)
                .values(resValList)
                .build());
        return resList;
    }

    private static int transOpModVoltWattRefType(EndDeviceProto.YRefType refType) {
        if (EndDeviceProto.YRefType.YRefTypeSetMaxW.equals(refType)) {
            return 0;
        } else if (EndDeviceProto.YRefType.YRefTypeStatWAvail.equals(refType)) {
            return 1;
        }
        return 0;
    }

    private static int transOpModVoltVarRefType(EndDeviceProto.YRefType refType) {
        if (EndDeviceProto.YRefType.YRefTypeSetMaxW.equals(refType)) {
            return 0;
        } else if (EndDeviceProto.YRefType.YRefTypeSetMaxVar.equals(refType)) {
            return 1;
        } else if (EndDeviceProto.YRefType.YRefTypeStatVarAvail.equals(refType)) {
            return 2;
        }
        return 0;
    }
}
