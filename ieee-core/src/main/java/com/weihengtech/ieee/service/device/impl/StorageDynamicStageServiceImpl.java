package com.weihengtech.ieee.service.device.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.weihengtech.ieee.EndDeviceProto;
import com.weihengtech.ieee.dao.StorageDynamicStageMapper;
import com.weihengtech.ieee.pojo.dos.StorageDynamicStageDO;
import com.weihengtech.ieee.pojo.dos.StorageEnergyInfoDO;
import com.weihengtech.ieee.service.device.StorageDynamicStageService;
import com.weihengtech.ieee.service.device.StorageEnergyInfoService;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.Date;

/**
 * <p>
 * 设备动态输出校验阶段 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-28
 */
@Service
public class StorageDynamicStageServiceImpl extends ServiceImpl<StorageDynamicStageMapper, StorageDynamicStageDO> implements StorageDynamicStageService {

    @Resource
    private StorageEnergyInfoService storageEnergyInfoService;

    @Override
    public StorageDynamicStageDO getByExportId(Integer exportId, Integer stage) {
        return getOne(Wrappers.<StorageDynamicStageDO>lambdaQuery()
                .eq(StorageDynamicStageDO::getStorageId, exportId)
                .eq(StorageDynamicStageDO::getStage, stage));
    }

    @Override
    public void updateStepState(EndDeviceProto.StepStateRequest request) {
        StorageEnergyInfoDO dynamicInfo = storageEnergyInfoService.getByDeviceId(request.getId());
        StorageDynamicStageDO stageInfo = getByExportId(dynamicInfo.getId(), request.getStage().getNumber());
        if (stageInfo != null) {
            if (stageInfo.getStageState() == 1) {
                return;
            }
            stageInfo.setStageState(request.getState().getNumber());
            stageInfo.setStageTime(new Date(request.getTimestamp()*1000));
            stageInfo.setErrMsg(request.getMessage());
            updateById(stageInfo);
            return;
        }
        save(StorageDynamicStageDO.builder()
                .storageId(dynamicInfo.getId())
                .stage(request.getStage().getNumber())
                .stageState(request.getState().getNumber())
                .stageTime(new Date(request.getTimestamp()*1000))
                .errMsg(request.getMessage())
                .build());
    }
}
