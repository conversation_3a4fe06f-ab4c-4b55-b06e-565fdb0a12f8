package com.weihengtech.ieee.service.modbus;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.weihengtech.ieee.enums.DeviceInfoEncodeTypeEnum;

import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class ModbusParseUtil {

	public static void parseMultiData(
			List<Integer> integerList, List<Map<String, String>> configList,
			List<Map<String, String>> result
	) {
		int start = Integer.parseInt(configList.get(0).get("start"));
		for (Map<String, String> config : configList) {
			try {
				List<Integer> fitList = ListUtil.sub(integerList, Integer.parseInt(config.get("start")) - start,
						Integer.parseInt(config.get("start")) - start + Integer.parseInt(config.get("len"))
				);
				config.put("decode", parseForDeviceInfoTypeEnum(infoTypeToTypeEnum(config.get("type")), fitList));
				result.add(config);
			} catch (Exception e) {
				config.put("decode", "");
			}
		}
	}

	public static void parseBatchMultiData(
			Map<String, List<Integer>> readValMap, List<List<Map<String, String>>> configBatchList,
			List<List<Map<String, String>>> result
	) {
		for (int i = 0; i < configBatchList.size(); i++) {
			List<Map<String, String>> configList = configBatchList.get(i);
			int start = Integer.parseInt(configList.get(0).get("start"));
			List<Map<String, String>> res = new ArrayList<>();
			for (Map<String, String> config : configList) {
				try {
					List<Integer> fitList = ListUtil.sub(readValMap.get(String.valueOf(i)), Integer.parseInt(config.get("start")) - start,
							Integer.parseInt(config.get("start")) - start + Integer.parseInt(config.get("len"))
					);
					config.put("decode", parseForDeviceInfoTypeEnum(infoTypeToTypeEnum(config.get("type")), fitList));
					res.add(config);
				} catch (Exception e) {
					config.put("decode", "");
				}
			}
			result.add(res);
		}
	}

	public static String parseForDeviceInfoTypeEnum(
			DeviceInfoEncodeTypeEnum deviceInfoEncodeTypeEnum,
			List<Integer> intList
	) {
		switch (deviceInfoEncodeTypeEnum) {
			case S16:
				return parseS16(intList);
			case S32:
				return parseS32(intList);
			case U16:
				return parseU16(intList);
			case U32:
				return parseU32(intList);
			case U32R:
				return parseU32R(intList);
			case BIT:
				return parseBit(intList);
			default:
				return parseAscii(intList);
		}
	}

	public static DeviceInfoEncodeTypeEnum infoTypeToTypeEnum(String type) {
		for (DeviceInfoEncodeTypeEnum typeEnum : DeviceInfoEncodeTypeEnum.values()) {
			if (typeEnum.name().equals(type)) {
				return typeEnum;
			}
		}
		return DeviceInfoEncodeTypeEnum.NONE;
	}

	public static String parseAscii(List<Integer> list) {
		List<Integer> parseAsciiList = new ArrayList<>();
		for (Integer l : list) {
			parseAsciiList.add(l / 256);
			parseAsciiList.add(l % 256);
		}
		StringBuilder stringBuilder = new StringBuilder();
		for (int num : parseAsciiList) {
			if (0 == num) {
				break;
			}
			stringBuilder.append((char) num);
		}
		return stringBuilder.toString();
	}

	public static String parseU16(List<Integer> list) {
		return new DecimalFormat().format(list.get(0)).replaceAll(",", "");
	}

	public static String parseU32(List<Integer> list) {
		double x = (long) list.get(0) << 16;
		double y = (long) list.get(1);
		return new DecimalFormat().format(x + y).replaceAll(",", "");
	}

	public static String parseU32R(List<Integer> list) {
		long x = (long) list.get(0);
		long y = (long) list.get(1) << 16;
		return new DecimalFormat().format(y + x).replaceAll(",", "");
	}

	public static String parseS16(List<? extends Number> list) {
		int x = list.get(0).intValue();
		return new DecimalFormat().format(x >= (1 << 15) ? x - (1 << 16) : x).replaceAll(",", "");
	}

	public static String parseS32(List<Integer> list) {
		long x = (long) list.get(0) << 16;
		long y = (long) list.get(1);
		long f = x + y;
		return new DecimalFormat().format((f >= (1L << 31)) ? (f - (1L << 32)) : f).replaceAll(",", "");
	}

	public static String parseBit(List<Integer> list) {
		StringBuilder stringBuilder = new StringBuilder();
		for (Integer integer : list) {
			String xBinaryStr = NumberUtil.getBinaryStr(integer);
			stringBuilder.append(StrUtil.fillBefore(xBinaryStr, '0', 16));
		}
		return stringBuilder.reverse().toString();
	}

	public static List<Integer> encodeAscii(String data, int len) {
		List<Integer> list = new ArrayList<>();
		int length = data.length();
		int count = 2;
		for (int i = 0; i < length; i += count) {
			int x = data.charAt(i) * 256;
			int y = 0;
			if (i <= length - 2) {
				y = data.charAt(i + 1);
			}
			list.add(x + y);
		}
		for (int i = list.size(); i < len; i++) {
			list.add(0);
		}
		return list;
	}

	public static Integer encodeU16(double data, Float multiply) {
		return (int) (data * multiply.doubleValue());
	}

	public static List<Integer> encodeU32(String data, Float multiply) {
		List<Double> doubleList = strToDoubleList(data);
		double r = doubleList.get(0) * multiply.doubleValue();
		return doubleToIntList(r);
	}

	public static Integer encodeS16(double data, Float multiply) {
		double x = data * multiply.doubleValue();
		if (x < 0) {
			x += 65536;
		}
		return (int) x;
	}

	public static List<Integer> encodeS32(String data, Float multiply) {
		List<Double> doubleList = strToDoubleList(data);
		double r = doubleList.get(0) * multiply.doubleValue();
		if (r < 0) {
			r += 2147483648D * 2;
		}
		return doubleToIntList(r);
	}

	private static List<Integer> doubleToIntList(double r) {
		return ListUtil.toLinkedList((int) (r / 65536), (int) (r % 65536));
	}

	private static List<Double> strToDoubleList(String data) {
		if (data.isEmpty()) {
			throw new RuntimeException("device config length error");
		}
		data = data.replace("[", "").replace("]", "");
		return Arrays.stream(data.split(",")).map(Double::valueOf).collect(Collectors.toList());
	}
}
