package com.weihengtech.ieee.service.modbus;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.ReflectUtil;
import com.weihengtech.ieee.pojo.bos.DeviceConfigBO;
import com.weihengtech.ieee.pojo.dos.DeviceListDO;
import com.weihengtech.ieee.pojo.vos.ReadBatchDeviceVO;
import com.weihengtech.ieee.pojo.vos.ReadDeviceVO;
import com.weihengtech.ieee.service.passthrough.SpecificServService;
import com.weihengtech.ieee.service.passthrough.StrategyService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class ModbusRequestUtil {

	@Resource
	private DeviceConfigBO deviceConfig;
	@Resource
	private StrategyService strategyService;

	public <T> void processDeviceConfig(DeviceListDO deviceInfo, T dto) {
		// 获取查询点位区间、点位与字段映射
		List<Integer> addressList = deviceConfig.getConfig().get(dto.getClass().getSimpleName());
		Map<String, Map<String, String>> detailMap = deviceConfig.getDetail().get(dto.getClass().getSimpleName());
		if (CollUtil.isEmpty(addressList) || CollUtil.isEmpty(detailMap)) {
			log.warn("config miss");
			throw new IllegalArgumentException("config miss");
		}
		Class<?> superclass = dto.getClass().getSuperclass();
		if (superclass != null && superclass != Object.class) {
			detailMap.putAll(deviceConfig.getDetail().get(superclass.getSimpleName()));
		}
		// 将点位数据两两分组，每组为一个起始、结束点位
		List<List<Integer>> splitAddressList = ListUtil.split(addressList, 2);
		// 进行批量透传读取
//		List<List<Map<String, String>>> resConfigList = readBatchConfigRes(deviceListDO, splitAddressList, detailMap);
		// 多线程进行透传读取
		List<List<Map<String, String>>> resConfigList = new ArrayList<>();
		for (List<Integer> splitAddr : splitAddressList) {
			List<Map<String, String>> singleRes = this.readConfigRes(deviceInfo, splitAddr, detailMap);
			resConfigList.add(singleRes);
		}
		// 构建属性值
		setDtoProperty(resConfigList, dto);
	}

	/** 构建属性值 */
	private <T> void setDtoProperty(List<List<Map<String, String>>> resConfigList, T dto) {
		for (List<Map<String, String>> list : resConfigList) {
			try {
				for (Map<String, String> config : list) {
					float realVal = Float.parseFloat(config.get("decode"));
					float multiply = Float.parseFloat(config.get("multiply"));
					if (multiply != 1.0) {
						realVal /= multiply;
					}
					ReflectUtil.setFieldValue(dto, config.get("prop"), realVal);
				}
			} catch (Exception e) {
				log.error(e.getMessage());
			}
		}
	}

	/**
	 * 批量透传读取
	 *
	 * @param deviceListDO 设备信息
	 * @param splitAddressList 批量地址
	 * @param detailMap 配置信息
	 * @return 批量透传结果
	 */
	private List<List<Map<String, String>>> readBatchConfigRes(DeviceListDO deviceListDO, List<List<Integer>> splitAddressList,
															   Map<String, Map<String, String>> detailMap) {
		if (CollUtil.isEmpty(splitAddressList)) {
			log.warn("config start end address error");
			throw new IllegalArgumentException("config start end address error");
		}
		// 批量读取地址
		List<ReadDeviceVO> valList = new ArrayList<>(splitAddressList.size());
		for (List<Integer> addressList : splitAddressList) {
			Integer start = addressList.get(0);
			Integer end = addressList.get(1);
			int len = end - start + 1;
			valList.add(ReadDeviceVO.builder().slaveId(1).startAddress(start).len(len).build());
		}

		// 从配置项中截取当前点位区间的详细配置数据
		List<List<Map<String, String>>> curDetailList = moduleBatchToConfigList(detailMap, valList);
		// 透传读取并解析数据
		return batchReadForModule(curDetailList, deviceListDO, valList);
	}

	/** 透传读取配置 */
	private List<Map<String, String>> readConfigRes(DeviceListDO deviceListDO, List<Integer> addressList, Map<String, Map<String, String>> detailMap) {
		if (CollUtil.isEmpty(addressList) || addressList.size() != 2) {
			log.warn("config start end address error");
			throw new IllegalArgumentException("config start end address error");
		}
		Integer start = addressList.get(0);
		Integer end = addressList.get(1);
		int len = end - start + 1;
		// 从配置项中截取当前点位区间的详细配置数据
		List<Map<String, String>> curDetailList = moduleToConfigList(detailMap, start, end);
		// 透传读取并解析数据
		return readForModule(curDetailList, deviceListDO, start, len, 1);
	}

	/** 从配置项中批量截取当前点位区间的详细配置数据 */
	public List<List<Map<String, String>>> moduleBatchToConfigList(
			Map<String, Map<String, String>> detailMap, List<ReadDeviceVO> valList) {
		List<List<Map<String, String>>> resList = new ArrayList<>(valList.size());
        for (ReadDeviceVO val : valList) {
            resList.add(detailMap.values().stream()
                    .filter(m -> Integer.parseInt(m.get("start")) >= val.getStartAddress()
							&& Integer.parseInt(m.get("start")) <= (val.getStartAddress() + val.getLen() - 1))
                    .sorted(Comparator.comparingInt(m -> Integer.parseInt(m.get("start")))).collect(Collectors.toList()));
        }
		return resList;
	}

	/** 从配置项中截取当前点位区间的详细配置数据 */
	public List<Map<String, String>> moduleToConfigList(
			Map<String, Map<String, String>> detailMap, int start, int end) {
		return detailMap.values().stream()
				.filter(m -> Integer.parseInt(m.get("start")) >= start && Integer.parseInt(m.get("start")) <= end)
				.sorted(Comparator.comparingInt(m -> Integer.parseInt(m.get("start")))).collect(Collectors.toList());
	}

	/** 透传读取并解析数据 */
	public List<Map<String, String>> readForModule(
			List<Map<String, String>> configList, DeviceListDO deviceListDO, int start, int len, Integer slaveId
	) {
		List<Map<String, String>> result = new ArrayList<>();
		// 透传
		List<Integer> integers = doRead(deviceListDO, start, len, slaveId);
		// 解析数据
		ModbusParseUtil.parseMultiData(integers, configList, result);
		return result;
	}

	public List<List<Map<String, String>>> batchReadForModule(
			List<List<Map<String, String>>> configList, DeviceListDO deviceListDO, List<ReadDeviceVO> valList
	) {
		List<List<Map<String, String>>> result = new ArrayList<>();
		// 透传
		Map<String, List<Integer>> resMap = doBatchRead(deviceListDO, valList);
		// 解析数据
		ModbusParseUtil.parseBatchMultiData(resMap, configList, result);
		return result;
	}

	/** 支持多类型设备透传 */
	public List<Integer> doRead(DeviceListDO deviceListDO, Integer startAddress, Integer len, Integer slaveId) {
		SpecificServService specificServService = strategyService.chooseSpecificServ(deviceListDO);
		log.info("设备读取开始：device: {}, startAddress: {}, len: {}", deviceListDO.getWifiSn(), startAddress, len);
		List<Integer> res = specificServService.sendReadCommand(ReadDeviceVO.builder()
						.deviceId(deviceListDO.getWifiSn())
						.startAddress(startAddress)
						.len(len)
						.slaveId(slaveId)
						.build()
		);
		log.info("设备读取结果：device：{}， {}", deviceListDO.getWifiSn(), res);
		return res;
	}

	/** 支持多类型设备批量透传 */
	public Map<String, List<Integer>> doBatchRead(DeviceListDO deviceListDO, List<ReadDeviceVO> valList) {
		SpecificServService specificServService = strategyService.chooseSpecificServ(deviceListDO);
		log.info("设备读取开始：device: {}, valList: {}", deviceListDO.getWifiSn(), valList);
		Map<String, List<Integer>> resMap = specificServService.sendBatchReadCommand(ReadBatchDeviceVO.builder()
				.deviceId(deviceListDO.getWifiSn())
				.valList(valList)
				.build()
		);
		log.info("设备读取结果：device:{}, {}", deviceListDO.getWifiSn(), resMap);
		return resMap;
	}
}
