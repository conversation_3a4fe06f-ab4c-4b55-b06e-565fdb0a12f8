package com.weihengtech.ieee.service.passthrough.impl;

import com.weihengtech.ieee.pojo.vos.ReadBatchDeviceVO;
import com.weihengtech.ieee.pojo.vos.ReadDeviceVO;
import com.weihengtech.ieee.pojo.vos.WriteBatchDeviceVO;
import com.weihengtech.ieee.pojo.vos.WriteDeviceVO;
import com.weihengtech.ieee.service.passthrough.IotClientService;
import com.weihengtech.ieee.service.passthrough.SpecificServService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Slf4j
@Service(value = "specificServ0")
public class SpecificServElinkServiceImpl implements SpecificServService {

	@Resource
	private IotClientService eLinkIotClient;

	@Override
	public List<Integer> sendReadCommand(ReadDeviceVO param) {
		return eLinkIotClient.readDevice(param);
	}

    @Override
    public Map<String, List<Integer>> sendBatchReadCommand(ReadBatchDeviceVO param) {
        return Collections.emptyMap();
    }

    @Override
	public Boolean sendWriteCommand(WriteDeviceVO param) {
		return eLinkIotClient.writeDevice(param);
	}

	@Override
	public Boolean sendWriteCommandAsync(WriteDeviceVO param) {
		return eLinkIotClient.writeDeviceAsync(param);
	}

	@Override
	public Boolean sendBatchWriteCommand(WriteBatchDeviceVO param) {
		return Boolean.FALSE;
	}
}
