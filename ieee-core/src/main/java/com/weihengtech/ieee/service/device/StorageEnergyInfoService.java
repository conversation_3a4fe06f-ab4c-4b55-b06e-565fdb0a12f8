package com.weihengtech.ieee.service.device;

import com.weihengtech.ieee.pojo.dos.StorageEnergyInfoDO;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * 设备扩展信息 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-02
 */
public interface StorageEnergyInfoService extends IService<StorageEnergyInfoDO> {

    /**
     * 根据设备id获取设备详情
     *
     * @param deviceId 设备id
     * @return 设备信息
     */
    StorageEnergyInfoDO getByDeviceId(Long deviceId);

    /**
     * 根据设备id获取设备详情
     *
     * @param lfdi lfdi
     * @return 设备信息
     */
    StorageEnergyInfoDO getByLfdi(String lfdi);

}
