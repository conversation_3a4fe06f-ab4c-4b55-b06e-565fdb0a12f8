package com.weihengtech.ieee.service.passthrough.impl;

import cn.hutool.json.JSONUtil;
import com.weihengtech.ieee.pojo.dtos.DeviceBasicInfoDTO;
import com.weihengtech.sdk.iot.ecos.EcosIotClient;
import com.weihengtech.sdk.iot.ecos.model.EcosIotResponse;
import com.weihengtech.sdk.iot.ecos.model.request.CloudSingleDeviceDetailRequest;
import com.weihengtech.sdk.iot.ecos.model.response.CloudDeviceDetailResponse;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;

import java.util.function.Function;

/**
 * iot服务实现
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/11/8 11:06
 */
@Slf4j
@Component
public class CacheServiceImpl {

    @Resource
    private EcosIotClient ecosIotClient;

    @Cacheable(value = "deviceBasicInfo", key = "#cloudId", unless = "#result == null")
    public DeviceBasicInfoDTO getDeviceBasicInfo(String cloudId, String cloud) {
        CloudSingleDeviceDetailRequest request = new CloudSingleDeviceDetailRequest(cloudId, cloud);
        CloudDeviceDetailResponse res = getIotCloudRes(request, ecosIotClient::queryCloudSingleDeviceDetail, "getDeviceBasicInfo");
        return DeviceBasicInfoDTO.builder()
                .id(res.getId())
                .online(res.getOnline())
                .category(res.getCategory())
                .productId(res.getProductId())
                .ip(res.getIp())
                .lat(res.getLat())
                .lon(res.getLon())
                .build();
    }


    /**
     * 执行iot接口并获取结果数据
     *
     * @param t 入参
     * @param function 执行方法
     * @param <P> 入参类型
     * @param <T> 返参类型
     * @return 结果
     */
    private <P, T> T getIotCloudRes(P t, Function<P, EcosIotResponse<T>> function, String funcName) {
        EcosIotResponse<T> response = function.apply(t);
        if (!response.getSuccess()) {
            throw new RuntimeException("call ecos iot error");
        }
        String resStr = response.getResult() instanceof Boolean ? String.valueOf(response.getResult()) : JSONUtil.toJsonStr(response.getResult());
        log.info("getIotCloudRes func is {}, param is : {}, res is {}", funcName, JSONUtil.toJsonStr(t), resStr);
        return response.getResult();
    }
}
