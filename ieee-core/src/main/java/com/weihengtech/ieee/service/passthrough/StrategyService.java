package com.weihengtech.ieee.service.passthrough;

import com.weihengtech.ieee.pojo.dos.DeviceListDO;
import com.weihengtech.ieee.pojo.dtos.passthrough.EndDeviceReadDataDTO;

/**
 * <AUTHOR>
 */
public interface StrategyService {

	/**
	 * 选择渠道服务
	 *
	 * @param deviceId 设备id
	 * @return 渠道服务实例
	 */
	SpecificServService chooseSpecificServ(String deviceId);

	/**
	 * 选择渠道服务
	 *
	 * @param deviceId 设备id
	 * @return 渠道服务实例
	 */
	SpecificServService chooseSpecificServ(DeviceListDO deviceListDO);

	/**
	 * 根据设备类型选择透传读取数据结构
	 *
	 * @param deviceType 设备类型
	 * @return 透传读取数据结构
	 */
	EndDeviceReadDataDTO chooseReadData(Integer company, String deviceType);
}
