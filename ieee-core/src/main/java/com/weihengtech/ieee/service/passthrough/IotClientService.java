package com.weihengtech.ieee.service.passthrough;

import com.weihengtech.ieee.pojo.vos.ReadBatchDeviceVO;
import com.weihengtech.ieee.pojo.vos.ReadDeviceVO;
import com.weihengtech.ieee.pojo.vos.WriteBatchDeviceVO;
import com.weihengtech.ieee.pojo.vos.WriteDeviceVO;
import com.weihengtech.sdk.iot.ecos.model.constant.CloudCategoryEnum;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * iot服务
 *
 * <AUTHOR>
 * @date 2023/11/8 14:23
 * @version 1.0
 */
public interface IotClientService {

	Boolean queryIsOnlineStatus(String cloudId);

	List<Integer> readDevice(ReadDeviceVO req);

	Map<String, List<Integer>> readBatchDevice(ReadBatchDeviceVO req);

	Boolean writeDevice(WriteDeviceVO req);

	Boolean writeDeviceAsync(WriteDeviceVO req);

	Boolean writeBatchDevice(WriteBatchDeviceVO req);

	/**
	 * 映射品类枚举
	 *
	 * @param category 品类
	 * @return 品类枚举
	 */
	default CloudCategoryEnum getEnumByCategory(String category) {
		return Arrays.stream(CloudCategoryEnum.values())
				.filter(i -> i.getValue().equals(category))
				.findFirst()
				.orElse(null);
	}

}
