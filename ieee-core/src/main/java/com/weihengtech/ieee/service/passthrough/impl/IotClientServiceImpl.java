package com.weihengtech.ieee.service.passthrough.impl;

import cn.hutool.json.JSONUtil;
import com.weihengtech.ieee.pojo.dtos.DeviceBasicInfoDTO;
import com.weihengtech.ieee.pojo.vos.ReadBatchDeviceVO;
import com.weihengtech.ieee.pojo.vos.ReadDeviceVO;
import com.weihengtech.ieee.pojo.vos.WriteBatchDeviceVO;
import com.weihengtech.ieee.pojo.vos.WriteDeviceVO;
import com.weihengtech.ieee.service.passthrough.IotClientService;
import com.weihengtech.sdk.iot.ecos.EcosIotClient;
import com.weihengtech.sdk.iot.ecos.model.EcosIotResponse;
import com.weihengtech.sdk.iot.ecos.model.constant.CloudCategoryEnum;
import com.weihengtech.sdk.iot.ecos.model.request.CloudDeviceOnlineStatusRequest;
import com.weihengtech.sdk.iot.ecos.model.request.EsBatchTransportReadCommand;
import com.weihengtech.sdk.iot.ecos.model.request.EsBatchTransportReadCommandRequest;
import com.weihengtech.sdk.iot.ecos.model.request.EsBatchTransportWriteCommand;
import com.weihengtech.sdk.iot.ecos.model.request.EsBatchTransportWriteCommandRequest;
import com.weihengtech.sdk.iot.ecos.model.request.EsTransportReadCommandRequest;
import com.weihengtech.sdk.iot.ecos.model.request.EsTransportWriteCommandRequest;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;

/**
 * iot服务实现
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/11/8 11:06
 */
@Slf4j
public class IotClientServiceImpl implements IotClientService {

    @Resource
    private CacheServiceImpl cacheService;
    @Resource
    private EcosIotClient ecosIotClient;

    /** 平台 */
    private final String cloud;

    /**
     * 指定构造方法
     * @param cloud 平台
     */
    public IotClientServiceImpl(String cloud) {
        this.cloud = cloud;
    }

    @Override
    public List<Integer> readDevice(ReadDeviceVO req) {
        DeviceBasicInfoDTO deviceBasicInfo = cacheService.getDeviceBasicInfo(req.getDeviceId(), cloud);
        CloudCategoryEnum cloudCategoryEnum = getEnumByCategory(deviceBasicInfo.getCategory());
        int funcCode = req.getStartAddress() >= 30000 && req.getStartAddress() <= 40000 ? 4 : 3;
        EsTransportReadCommandRequest request = new EsTransportReadCommandRequest(
                req.getDeviceId(), req.getSlaveId(), req.getStartAddress(), req.getLen(), funcCode,
                cloud, cloudCategoryEnum, deviceBasicInfo.getProductId());
        return getIotCloudRes(request, ecosIotClient :: sendEsTransportReadCommand, "readDevice");
    }

    @Override
    public Map<String, List<Integer>> readBatchDevice(ReadBatchDeviceVO req) {
        DeviceBasicInfoDTO deviceBasicInfo = cacheService.getDeviceBasicInfo(req.getDeviceId(), cloud);
        CloudCategoryEnum cloudCategoryEnum = getEnumByCategory(deviceBasicInfo.getCategory());
        List<EsBatchTransportReadCommand> valList = new ArrayList<>(req.getValList().size());
        for (int i = 0; i < req.getValList().size(); i++) {
            ReadDeviceVO item = req.getValList().get(i);
            valList.add(new EsBatchTransportReadCommand(item.getSlaveId(), item.getStartAddress(), item.getLen(),
                    item.getStartAddress() >= 30000 && item.getStartAddress() <= 40000 ? 4 : 3, i));
        }
        EsBatchTransportReadCommandRequest request = new EsBatchTransportReadCommandRequest(req.getDeviceId(),
                valList, cloud, cloudCategoryEnum, deviceBasicInfo.getProductId());
        return getIotCloudRes(request, ecosIotClient :: sendEsBatchTransportReadCommand, "readBatchDevice");
    }

    @Override
    public Boolean writeDevice(WriteDeviceVO req) {
        DeviceBasicInfoDTO deviceBasicInfo = cacheService.getDeviceBasicInfo(req.getDeviceId(), cloud);
        CloudCategoryEnum cloudCategoryEnum = getEnumByCategory(deviceBasicInfo.getCategory());
        EsTransportWriteCommandRequest request = new EsTransportWriteCommandRequest(
                req.getDeviceId(), req.getSlaveId(), req.getStartAddress(), req.getLen(),
                req.getValues(), cloud, cloudCategoryEnum, deviceBasicInfo.getProductId()
        );
        return getIotCloudRes(request, ecosIotClient :: sendEsTransportWriteCommand, "writeDevice");
    }

    @Override
    public Boolean writeDeviceAsync(WriteDeviceVO req) {
        DeviceBasicInfoDTO deviceBasicInfo = cacheService.getDeviceBasicInfo(req.getDeviceId(), cloud);
        CloudCategoryEnum cloudCategoryEnum = getEnumByCategory(deviceBasicInfo.getCategory());
        EsTransportWriteCommandRequest request = new EsTransportWriteCommandRequest(
                req.getDeviceId(), req.getSlaveId(), req.getStartAddress(), req.getLen(),
                req.getValues(), cloud, cloudCategoryEnum, deviceBasicInfo.getProductId()
        );
        return getIotCloudRes(request, ecosIotClient :: sendEsTransportWriteCommandAsync, "writeDeviceAsync");
    }

    @Override
    public Boolean writeBatchDevice(WriteBatchDeviceVO req) {
        DeviceBasicInfoDTO deviceBasicInfo = cacheService.getDeviceBasicInfo(req.getDeviceId(), cloud);
        CloudCategoryEnum cloudCategoryEnum = getEnumByCategory(deviceBasicInfo.getCategory());
        List<EsBatchTransportWriteCommand> valList = new ArrayList<>(req.getValList().size());
        for (int i = 0; i < req.getValList().size(); i++) {
            WriteDeviceVO item = req.getValList().get(i);
            valList.add(new EsBatchTransportWriteCommand(item.getSlaveId(), item.getStartAddress(), item.getLen(), i, item.getValues()));
        }
        EsBatchTransportWriteCommandRequest request = new EsBatchTransportWriteCommandRequest(
                req.getDeviceId(), valList, cloud, cloudCategoryEnum, deviceBasicInfo.getProductId()
        );
        return getIotCloudRes(request, ecosIotClient :: sendEsBatchTransportWriteCommand, "writeBatchDevice");
    }

    @Override
    public Boolean queryIsOnlineStatus(String cloudId) {
        CloudDeviceOnlineStatusRequest request = new CloudDeviceOnlineStatusRequest(cloudId, cloud);
        return getIotCloudRes(request, ecosIotClient::queryCloudDeviceOnlineStatus, "queryIsOnlineStatus");
    }


    /**
     * 执行iot接口并获取结果数据
     *
     * @param t 入参
     * @param function 执行方法
     * @param <P> 入参类型
     * @param <T> 返参类型
     * @return 结果
     */
    private <P, T> T getIotCloudRes(P t, Function<P, EcosIotResponse<T>> function, String funcName) {
        EcosIotResponse<T> response = function.apply(t);
        if (!response.getSuccess()) {
            throw new RuntimeException("call ecos iot error");
        }
        String resStr = response.getResult() instanceof Boolean ? String.valueOf(response.getResult()) : JSONUtil.toJsonStr(response.getResult());
        log.info("getIotCloudRes func is {}, param is : {}, res is {}", funcName, JSONUtil.toJsonStr(t), resStr);
        return response.getResult();
    }
}
