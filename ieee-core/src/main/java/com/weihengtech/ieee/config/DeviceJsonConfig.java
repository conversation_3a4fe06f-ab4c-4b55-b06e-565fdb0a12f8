package com.weihengtech.ieee.config;

import cn.hutool.core.io.resource.ResourceUtil;
import cn.hutool.json.JSONUtil;
import com.weihengtech.ieee.pojo.bos.DeviceConfigBO;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/8/5 15:27
 */
@Configuration
public class DeviceJsonConfig {

    @Bean
    public DeviceConfigBO deviceConfig() {
        String deviceJsonStr = ResourceUtil.readUtf8Str("device.json");
        return JSONUtil.toBean(deviceJsonStr, DeviceConfigBO.class);
    }
}
