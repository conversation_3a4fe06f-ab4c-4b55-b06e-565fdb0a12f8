package com.weihengtech.ieee.config;

import com.weihengtech.ieee.service.passthrough.IotClientService;
import com.weihengtech.ieee.service.passthrough.impl.IotClientServiceImpl;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * iot client配置
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/11/9 9:49
 */
@Configuration
public class IotClientConfig {

    @Value("${sdk.iot.ecos.flag.elink:0}")
    private String elinkCloud;

    @Value("${sdk.iot.ecos.flag.tuya:1}")
    private String tuyaCloud;

    @Value("${sdk.iot.ecos.flag.ocpp:2}")
    private String ocppCloud;

    @Value("${sdk.iot.ecos.flag.wh:3}")
    private String whCloud;

    @Bean
    IotClientService tuyaIotClient() {
        return new IotClientServiceImpl(tuyaCloud);
    }

    @Bean
    IotClientService eLinkIotClient() {
        return new IotClientServiceImpl(elinkCloud);
    }

    @Bean
    IotClientService ocppIotClient() {
        return new IotClientServiceImpl(ocppCloud);
    }

    @Bean
    IotClientService whIotClient() {
        return new IotClientServiceImpl(whCloud);
    }
}
