package com.weihengtech.ieee.generator;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.generator.FastAutoGenerator;
import com.baomidou.mybatisplus.generator.config.OutputFile;
import com.baomidou.mybatisplus.generator.config.rules.DateType;
import com.baomidou.mybatisplus.generator.config.rules.NamingStrategy;
import com.baomidou.mybatisplus.generator.engine.VelocityTemplateEngine;

import java.util.Collections;

/**
 * 逆向工程生成器 - 适配 MyBatis Plus 3.5.x
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/24 16:22
 */
public class MybatisPlusGenerator {

    private static final String URL = "****************************************************************************************************************************";
    private static final String USERNAME = "root";
    private static final String PASSWORD = "test1234";

    /** 这里输入要逆向工程的表名 */
    private static final String[] TABLE_NAMES = new String[]{"storage_energy_info"};

    public static void main(String[] args) {
        String projectPath = System.getProperty("user.dir");

        FastAutoGenerator.create(URL, USERNAME, PASSWORD)
                .globalConfig(builder -> {
                    builder.author("lujie.shen") // 设置作者
                            .enableSwagger() // 开启 swagger 模式
                            .outputDir(projectPath + "/ieee-core/src/main/java") // 指定输出目录
                            .dateType(DateType.ONLY_DATE) // 时间策略
                            .commentDate("yyyy-MM-dd"); // 注释日期
                })
                .dataSourceConfig(builder -> builder.typeConvertHandler((globalConfig, typeRegistry, metaInfo) -> {
                    // 自定义类型转换
                    return typeRegistry.getColumnType(metaInfo);
                }))
                .packageConfig(builder -> {
                    builder.parent("com.weihengtech.ieee") // 设置父包名
                            .entity("pojo.dos") // 设置实体类包名
                            .mapper("dao") // 设置 Mapper 接口包名
                            .service("service") // 设置 Service 接口包名
                            .serviceImpl("service.impl") // 设置 Service 实现类包名
                            .pathInfo(Collections.singletonMap(OutputFile.xml, projectPath + "/ieee-core/src/main/resources/mapper")); // 设置mapperXml生成路径
                })
                .strategyConfig(builder -> {
                    builder.addInclude(TABLE_NAMES) // 设置需要生成的表名
                            .addTablePrefix("t_", "c_") // 设置过滤表前缀
                            .entityBuilder()
                            .enableLombok() // 开启 lombok 模式
                            .enableTableFieldAnnotation() // 开启生成实体时生成字段注解
                            .naming(NamingStrategy.underline_to_camel) // 数据库表映射到实体的命名策略
                            .columnNaming(NamingStrategy.underline_to_camel) // 数据库表字段映射到实体的命名策略
                            .idType(IdType.ASSIGN_ID) // 全局主键类型
                            .formatFileName("%sDO") // 格式化文件名称
                            .mapperBuilder()
                            .enableMapperAnnotation() // 开启 @Mapper 注解
                            .formatMapperFileName("%sMapper") // 格式化 mapper 文件名称
                            .formatXmlFileName("%sMapper") // 格式化 xml 实现类文件名称
                            .serviceBuilder()
                            .formatServiceFileName("%sService") // 格式化 service 接口文件名称
                            .formatServiceImplFileName("%sServiceImpl"); // 格式化 service 实现类文件名称
                })
                .templateEngine(new VelocityTemplateEngine()) // 使用Velocity引擎模板，默认的是Velocity引擎模板
                .execute();
    }
}
