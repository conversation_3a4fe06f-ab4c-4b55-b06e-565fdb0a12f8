2025-09-23T15:50:32.536+08:00  INFO 15428 --- [main] c.w.ieee.IeeeServerApplication           : Starting IeeeServerApplication using Java 21.0.6 with PID 15428 (D:\Program Files\JetBrains\IdeaProjects\ieee-protocol\ieee-server\target\classes started by lujie.shen in D:\Program Files\JetBrains\IdeaProjects\ieee-protocol)
2025-09-23T15:50:32.542+08:00  INFO 15428 --- [main] c.w.ieee.IeeeServerApplication           : No active profile set, falling back to 1 default profile: "default"
2025-09-23T15:50:33.456+08:00  INFO 15428 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Multiple Spring Data modules found, entering strict repository configuration mode
2025-09-23T15:50:33.459+08:00  INFO 15428 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-09-23T15:50:33.503+08:00  INFO 15428 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 30 ms. Found 0 Redis repository interfaces.
2025-09-23T15:50:34.330+08:00  INFO 15428 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8080 (http)
2025-09-23T15:50:34.338+08:00  INFO 15428 --- [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-09-23T15:50:34.338+08:00  INFO 15428 --- [main] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.15]
2025-09-23T15:50:34.409+08:00  INFO 15428 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-09-23T15:50:34.409+08:00  INFO 15428 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1814 ms
2025-09-23T15:50:35.628+08:00  INFO 15428 --- [main] g.s.a.GrpcServerFactoryAutoConfiguration : Detected grpc-netty-shaded: Creating ShadedNettyGrpcServerFactory
2025-09-23T15:50:35.929+08:00  INFO 15428 --- [main] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 13 endpoint(s) beneath base path '/actuator'
2025-09-23T15:50:35.979+08:00  INFO 15428 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 8080 (http) with context path ''
2025-09-23T15:50:36.054+08:00  INFO 15428 --- [main] n.d.b.g.s.s.AbstractGrpcServerFactory    : Registered gRPC service: rpc.EndDeviceService, bean: endDeviceServer, class: com.weihengtech.ieee.server.EndDeviceServer
2025-09-23T15:50:36.054+08:00  INFO 15428 --- [main] n.d.b.g.s.s.AbstractGrpcServerFactory    : Registered gRPC service: grpc.health.v1.Health, bean: grpcHealthService, class: io.grpc.protobuf.services.HealthServiceImpl
2025-09-23T15:50:36.054+08:00  INFO 15428 --- [main] n.d.b.g.s.s.AbstractGrpcServerFactory    : Registered gRPC service: grpc.reflection.v1alpha.ServerReflection, bean: protoReflectionService, class: io.grpc.protobuf.services.ProtoReflectionService
2025-09-23T15:50:36.056+08:00  INFO 15428 --- [main] c.w.ieee.config.GrpcServerConfiguration  : gRPC server configured to use virtual threads executor
2025-09-23T15:50:36.197+08:00  INFO 15428 --- [main] n.d.b.g.s.s.GrpcServerLifecycle          : gRPC Server started, listening on address: *, port: 9090
2025-09-23T15:50:36.207+08:00  INFO 15428 --- [main] c.w.ieee.IeeeServerApplication           : Started IeeeServerApplication in 4.046 seconds (process running for 4.822)
2025-09-23T15:50:39.214+08:00  INFO 15428 --- [main] c.w.ieee.config.VirtualThreadVerifier    : === Virtual Thread Configuration Verification ===
2025-09-23T15:50:39.215+08:00  INFO 15428 --- [main] c.w.ieee.config.VirtualThreadVerifier    : Java Version: 21.0.6
2025-09-23T15:50:39.226+08:00  INFO 15428 --- [main] c.w.ieee.config.VirtualThreadVerifier    : Virtual Thread Support: true
2025-09-23T15:50:39.226+08:00  INFO 15428 --- [main] c.w.ieee.config.VirtualThreadVerifier    : Current Thread: Thread[#1,main,5,main]
2025-09-23T15:50:39.226+08:00  INFO 15428 --- [main] c.w.ieee.config.VirtualThreadVerifier    : Current Thread Name: main
2025-09-23T15:50:39.226+08:00  INFO 15428 --- [main] c.w.ieee.config.VirtualThreadVerifier    : Current Thread Class: java.lang.Thread
2025-09-23T15:50:39.227+08:00  INFO 15428 --- [test-virtual-thread] c.w.ieee.config.VirtualThreadVerifier    : Running in virtual thread: VirtualThread[#86,test-virtual-thread]/runnable@ForkJoinPool-1-worker-1
2025-09-23T15:50:39.228+08:00  INFO 15428 --- [test-virtual-thread] c.w.ieee.config.VirtualThreadVerifier    : Virtual thread name: test-virtual-thread
2025-09-23T15:50:39.228+08:00  INFO 15428 --- [test-virtual-thread] c.w.ieee.config.VirtualThreadVerifier    : Virtual thread class: java.lang.VirtualThread
2025-09-23T15:50:39.228+08:00  INFO 15428 --- [test-virtual-thread] c.w.ieee.config.VirtualThreadVerifier    : Is virtual thread: true
2025-09-23T15:50:39.228+08:00  INFO 15428 --- [main] c.w.ieee.config.VirtualThreadVerifier    : === gRPC Virtual Thread Configuration Applied ===
2025-09-23T15:50:39.228+08:00  INFO 15428 --- [main] c.w.ieee.config.VirtualThreadVerifier    : gRPC server should now use virtual threads instead of grpc-default-executor threads
2025-09-23T15:50:39.228+08:00  INFO 15428 --- [main] c.w.ieee.config.VirtualThreadVerifier    : Monitor thread names in logs to verify - should see virtual thread names instead of grpc-default-executor-*
2025-09-23T15:50:39.232+08:00  INFO 15428 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#89]/runnable@ForkJoinPool-1-worker-1 start
2025-09-23T15:50:39.232+08:00  INFO 15428 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#90]/runnable@ForkJoinPool-1-worker-3 start
2025-09-23T15:50:39.234+08:00  INFO 15428 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#104]/runnable@ForkJoinPool-1-worker-5 start
2025-09-23T15:50:39.238+08:00  INFO 15428 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#135]/runnable@ForkJoinPool-1-worker-10 start
2025-09-23T15:50:39.238+08:00  INFO 15428 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#137]/runnable@ForkJoinPool-1-worker-18 start
2025-09-23T15:50:39.238+08:00  INFO 15428 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#138]/runnable@ForkJoinPool-1-worker-12 start
2025-09-23T15:50:39.239+08:00  INFO 15428 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#141]/runnable@ForkJoinPool-1-worker-7 start
2025-09-23T15:50:39.239+08:00  INFO 15428 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#142]/runnable@ForkJoinPool-1-worker-16 start
2025-09-23T15:50:39.239+08:00  INFO 15428 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#144]/runnable@ForkJoinPool-1-worker-17 start
2025-09-23T15:50:39.232+08:00  INFO 15428 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#91]/runnable@ForkJoinPool-1-worker-2 start
2025-09-23T15:50:39.233+08:00  INFO 15428 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#93]/runnable@ForkJoinPool-1-worker-2 start
2025-09-23T15:50:39.239+08:00  INFO 15428 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#145]/runnable@ForkJoinPool-1-worker-13 start
2025-09-23T15:50:39.240+08:00  INFO 15428 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#146]/runnable@ForkJoinPool-1-worker-15 start
2025-09-23T15:50:39.240+08:00  INFO 15428 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#150]/runnable@ForkJoinPool-1-worker-8 start
2025-09-23T15:50:39.240+08:00  INFO 15428 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#151]/runnable@ForkJoinPool-1-worker-5 start
2025-09-23T15:50:39.240+08:00  INFO 15428 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#152]/runnable@ForkJoinPool-1-worker-14 start
2025-09-23T15:50:39.240+08:00  INFO 15428 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#156]/runnable@ForkJoinPool-1-worker-9 start
2025-09-23T15:50:39.240+08:00  INFO 15428 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#162]/runnable@ForkJoinPool-1-worker-7 start
2025-09-23T15:50:39.233+08:00  INFO 15428 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#92]/runnable@ForkJoinPool-1-worker-4 start
2025-09-23T15:50:39.241+08:00  INFO 15428 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#163]/runnable@ForkJoinPool-1-worker-6 start
2025-09-23T15:50:39.241+08:00  INFO 15428 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#165]/runnable@ForkJoinPool-1-worker-17 start
2025-09-23T15:50:39.241+08:00  INFO 15428 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#167]/runnable@ForkJoinPool-1-worker-2 start
2025-09-23T15:50:39.241+08:00  INFO 15428 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#168]/runnable@ForkJoinPool-1-worker-1 start
2025-09-23T15:50:39.241+08:00  INFO 15428 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#170]/runnable@ForkJoinPool-1-worker-11 start
2025-09-23T15:50:39.241+08:00  INFO 15428 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#169]/runnable@ForkJoinPool-1-worker-13 start
2025-09-23T15:50:39.242+08:00  INFO 15428 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#171]/runnable@ForkJoinPool-1-worker-8 start
2025-09-23T15:50:39.242+08:00  INFO 15428 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#172]/runnable@ForkJoinPool-1-worker-15 start
2025-09-23T15:50:39.242+08:00  INFO 15428 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#176]/runnable@ForkJoinPool-1-worker-18 start
2025-09-23T15:50:39.242+08:00  INFO 15428 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#177]/runnable@ForkJoinPool-1-worker-14 start
2025-09-23T15:50:39.242+08:00  INFO 15428 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#180]/runnable@ForkJoinPool-1-worker-9 start
2025-09-23T15:50:39.242+08:00  INFO 15428 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#179]/runnable@ForkJoinPool-1-worker-12 start
2025-09-23T15:50:39.243+08:00  INFO 15428 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#196]/runnable@ForkJoinPool-1-worker-10 start
2025-09-23T15:50:39.243+08:00  INFO 15428 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#186]/runnable@ForkJoinPool-1-worker-2 start
2025-09-23T15:50:39.233+08:00  INFO 15428 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#94]/runnable@ForkJoinPool-1-worker-5 start
2025-09-23T15:50:39.243+08:00  INFO 15428 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#187]/runnable@ForkJoinPool-1-worker-19 start
2025-09-23T15:50:39.243+08:00  INFO 15428 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#191]/runnable@ForkJoinPool-1-worker-11 start
2025-09-23T15:50:39.243+08:00  INFO 15428 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#194]/runnable@ForkJoinPool-1-worker-8 start
2025-09-23T15:50:39.233+08:00  INFO 15428 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#96]/runnable@ForkJoinPool-1-worker-4 start
2025-09-23T15:50:39.233+08:00  INFO 15428 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#95]/runnable@ForkJoinPool-1-worker-6 start
2025-09-23T15:50:39.234+08:00  INFO 15428 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#99]/runnable@ForkJoinPool-1-worker-5 start
2025-09-23T15:50:39.234+08:00  INFO 15428 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#100]/runnable@ForkJoinPool-1-worker-4 start
2025-09-23T15:50:39.234+08:00  INFO 15428 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#102]/runnable@ForkJoinPool-1-worker-6 start
2025-09-23T15:50:39.235+08:00  INFO 15428 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#105]/runnable@ForkJoinPool-1-worker-1 start
2025-09-23T15:50:39.235+08:00  INFO 15428 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#106]/runnable@ForkJoinPool-1-worker-4 start
2025-09-23T15:50:39.235+08:00  INFO 15428 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#108]/runnable@ForkJoinPool-1-worker-13 start
2025-09-23T15:50:39.240+08:00  INFO 15428 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#148]/runnable@ForkJoinPool-1-worker-4 start
2025-09-23T15:50:39.240+08:00  INFO 15428 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#153]/runnable@ForkJoinPool-1-worker-10 start
2025-09-23T15:50:39.240+08:00  INFO 15428 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#154]/runnable@ForkJoinPool-1-worker-18 start
2025-09-23T15:50:39.235+08:00  INFO 15428 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#111]/runnable@ForkJoinPool-1-worker-4 start
2025-09-23T15:50:39.241+08:00  INFO 15428 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#164]/runnable@ForkJoinPool-1-worker-16 start
2025-09-23T15:50:39.242+08:00  INFO 15428 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#173]/runnable@ForkJoinPool-1-worker-5 start
2025-09-23T15:50:39.236+08:00  INFO 15428 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#112]/runnable@ForkJoinPool-1-worker-12 start
2025-09-23T15:50:39.242+08:00  INFO 15428 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#174]/runnable@ForkJoinPool-1-worker-10 start
2025-09-23T15:50:39.236+08:00  INFO 15428 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#113]/runnable@ForkJoinPool-1-worker-9 start
2025-09-23T15:50:39.236+08:00  INFO 15428 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#114]/runnable@ForkJoinPool-1-worker-3 start
2025-09-23T15:50:39.236+08:00  INFO 15428 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#115]/runnable@ForkJoinPool-1-worker-17 start
2025-09-23T15:50:39.236+08:00  INFO 15428 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#101]/runnable@ForkJoinPool-1-worker-7 start
2025-09-23T15:50:39.236+08:00  INFO 15428 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#117]/runnable@ForkJoinPool-1-worker-6 start
2025-09-23T15:50:39.236+08:00  INFO 15428 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#116]/runnable@ForkJoinPool-1-worker-13 start
2025-09-23T15:50:39.236+08:00  INFO 15428 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#118]/runnable@ForkJoinPool-1-worker-15 start
2025-09-23T15:50:39.236+08:00  INFO 15428 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#119]/runnable@ForkJoinPool-1-worker-1 start
2025-09-23T15:50:39.237+08:00  INFO 15428 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#120]/runnable@ForkJoinPool-1-worker-4 start
2025-09-23T15:50:39.237+08:00  INFO 15428 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#97]/runnable@ForkJoinPool-1-worker-8 start
2025-09-23T15:50:39.237+08:00  INFO 15428 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#125]/runnable@ForkJoinPool-1-worker-3 start
2025-09-23T15:50:39.237+08:00  INFO 15428 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#124]/runnable@ForkJoinPool-1-worker-12 start
2025-09-23T15:50:39.237+08:00  INFO 15428 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#122]/runnable@ForkJoinPool-1-worker-9 start
2025-09-23T15:50:39.237+08:00  INFO 15428 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#127]/runnable@ForkJoinPool-1-worker-7 start
2025-09-23T15:50:39.237+08:00  INFO 15428 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#123]/runnable@ForkJoinPool-1-worker-16 start
2025-09-23T15:50:39.237+08:00  INFO 15428 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#128]/runnable@ForkJoinPool-1-worker-6 start
2025-09-23T15:50:39.237+08:00  INFO 15428 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#126]/runnable@ForkJoinPool-1-worker-17 start
2025-09-23T15:50:39.237+08:00  INFO 15428 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#129]/runnable@ForkJoinPool-1-worker-13 start
2025-09-23T15:50:39.237+08:00  INFO 15428 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#130]/runnable@ForkJoinPool-1-worker-15 start
2025-09-23T15:50:39.238+08:00  INFO 15428 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#132]/runnable@ForkJoinPool-1-worker-1 start
2025-09-23T15:50:39.238+08:00  INFO 15428 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#133]/runnable@ForkJoinPool-1-worker-4 start
2025-09-23T15:50:39.238+08:00  INFO 15428 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#103]/runnable@ForkJoinPool-1-worker-11 start
2025-09-23T15:50:39.238+08:00  INFO 15428 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#134]/runnable@ForkJoinPool-1-worker-14 start
2025-09-23T15:50:39.238+08:00  INFO 15428 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#136]/runnable@ForkJoinPool-1-worker-8 start
2025-09-23T15:50:39.238+08:00  INFO 15428 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#139]/runnable@ForkJoinPool-1-worker-3 start
2025-09-23T15:50:39.238+08:00  INFO 15428 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#140]/runnable@ForkJoinPool-1-worker-9 start
2025-09-23T15:50:39.239+08:00  INFO 15428 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#143]/runnable@ForkJoinPool-1-worker-6 start
2025-09-23T15:50:39.240+08:00  INFO 15428 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#147]/runnable@ForkJoinPool-1-worker-1 start
2025-09-23T15:50:39.240+08:00  INFO 15428 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#157]/runnable@ForkJoinPool-1-worker-12 start
2025-09-23T15:50:39.242+08:00  INFO 15428 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#175]/runnable@ForkJoinPool-1-worker-3 start
2025-09-23T15:50:39.243+08:00  INFO 15428 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#198]/runnable@ForkJoinPool-1-worker-18 start
2025-09-23T15:50:39.243+08:00  INFO 15428 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#181]/runnable@ForkJoinPool-1-worker-7 start
2025-09-23T15:50:39.243+08:00  INFO 15428 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#188]/runnable@ForkJoinPool-1-worker-1 start
2025-09-23T15:50:39.243+08:00  INFO 15428 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#189]/runnable@ForkJoinPool-1-worker-13 start
2025-09-23T15:50:39.235+08:00  INFO 15428 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#109]/runnable@ForkJoinPool-1-worker-6 start
2025-09-23T15:50:39.235+08:00  INFO 15428 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#110]/runnable@ForkJoinPool-1-worker-1 start
2025-09-23T15:50:39.243+08:00  INFO 15428 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#182]/runnable@ForkJoinPool-1-worker-16 start
2025-09-23T15:50:39.243+08:00  INFO 15428 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#184]/runnable@ForkJoinPool-1-worker-17 start
2025-09-23T15:50:39.236+08:00  INFO 15428 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#98]/runnable@ForkJoinPool-1-worker-10 start
2025-09-23T15:50:39.243+08:00  INFO 15428 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#192]/runnable@ForkJoinPool-1-worker-3 start
2025-09-23T15:50:39.237+08:00  INFO 15428 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#121]/runnable@ForkJoinPool-1-worker-10 start
2025-09-23T15:50:39.243+08:00  INFO 15428 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#183]/runnable@ForkJoinPool-1-worker-6 start
2025-09-23T15:50:39.240+08:00  INFO 15428 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#149]/runnable@ForkJoinPool-1-worker-11 start
2025-09-23T15:50:39.243+08:00  INFO 15428 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#197]/runnable@ForkJoinPool-1-worker-5 start
2025-09-23T15:50:39.240+08:00  INFO 15428 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#155]/runnable@ForkJoinPool-1-worker-3 start
2025-09-23T15:50:39.241+08:00  INFO 15428 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#166]/runnable@ForkJoinPool-1-worker-19 start
2025-09-23T15:50:39.243+08:00  INFO 15428 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#195]/runnable@ForkJoinPool-1-worker-15 start
2025-09-23T15:50:39.579+08:00  INFO 15428 --- [RMI TCP Connection(5)-***********] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-09-23T15:50:39.579+08:00  INFO 15428 --- [RMI TCP Connection(5)-***********] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-09-23T15:50:39.581+08:00  INFO 15428 --- [RMI TCP Connection(5)-***********] o.s.web.servlet.DispatcherServlet        : Completed initialization in 2 ms
2025-09-23T15:50:39.608+08:00  INFO 15428 --- [RMI TCP Connection(4)-***********] com.alibaba.druid.pool.DruidDataSource   : {dataSource-1} inited
