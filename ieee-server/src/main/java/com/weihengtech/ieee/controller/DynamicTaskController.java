package com.weihengtech.ieee.controller;

import com.weihengtech.ieee.handler.DynamicTaskHandler;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import java.util.Collection;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/7/24 15:03
 */
@RestController
@RequestMapping("/task")
public class DynamicTaskController {

    @Resource
    private DynamicTaskHandler dynamicTaskHandler;
    @Resource
    private ThreadPoolTaskScheduler taskScheduler;

    @GetMapping("/statistics")
    public String statistics() {
        Collection<String> tasks = dynamicTaskHandler.activeTasks();
        return tasks.toString();
    }

    @GetMapping("/remove")
    public String removeTask(@RequestParam String taskName) {
        dynamicTaskHandler.taskMap.remove(taskName);
        Collection<String> tasks = dynamicTaskHandler.activeTasks();
        return tasks.toString();
    }

    @PostMapping("/adjust_pool_size")
    public void adjustPoolSize(@RequestParam int newSize) {
        taskScheduler.setPoolSize(newSize);
    }

}
