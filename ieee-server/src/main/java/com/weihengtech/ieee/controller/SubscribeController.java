package com.weihengtech.ieee.controller;

import com.weihengtech.ieee.EndDeviceProto;
import com.weihengtech.ieee.pojo.dos.StorageEnergyInfoDO;
import com.weihengtech.ieee.pojo.vos.SubscribeVO;
import com.weihengtech.ieee.server.EndDeviceServer;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/7/24 15:03
 */
@RestController
@RequestMapping("/subscribe")
public class SubscribeController {

    @PostMapping("/listen")
    public void listen(@RequestBody SubscribeVO param) {
        try {
            if (EndDeviceServer.RESPONSE_OBSERVER != null) {
                List<EndDeviceProto.EndDevice> endDevices = StorageEnergyInfoDO.buildEndDeviceList(Collections.singletonList(param.getItem()));
                EndDeviceServer.RESPONSE_OBSERVER.onNext(EndDeviceProto.SubscribeEndDeviceResponse.newBuilder()
                        .setType(EndDeviceProto.SubscriptionType.valueOf(param.getType()))
                        .setEndDevice(endDevices.get(0))
                        .setElectricPowerCompany(param.getItem().getGridCompany())
                        .build());
            }
        } catch (IllegalArgumentException e) {
            EndDeviceServer.RESPONSE_OBSERVER.onCompleted();
        }
    }

}
