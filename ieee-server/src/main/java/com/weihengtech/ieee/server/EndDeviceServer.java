package com.weihengtech.ieee.server;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.weihengtech.ieee.EmptyProto;
import com.weihengtech.ieee.EndDeviceProto;
import com.weihengtech.ieee.EndDeviceServiceGrpc;
import com.weihengtech.ieee.constants.Constants;
import com.weihengtech.ieee.pojo.dos.DeviceListDO;
import com.weihengtech.ieee.pojo.dos.StorageEnergyInfoDO;
import com.weihengtech.ieee.pojo.dtos.passthrough.EndDeviceReadDataDTO;
import com.weihengtech.ieee.service.device.DeviceListService;
import com.weihengtech.ieee.service.device.EndDeviceService;
import com.weihengtech.ieee.service.device.StorageDynamicStageService;
import com.weihengtech.ieee.service.device.StorageEnergyInfoService;
import com.weihengtech.ieee.service.modbus.ModbusRequestUtil;
import com.weihengtech.ieee.service.passthrough.StrategyService;
import io.grpc.Status;
import io.grpc.stub.StreamObserver;
import lombok.extern.slf4j.Slf4j;
import net.devh.boot.grpc.server.service.GrpcService;
import org.springframework.data.redis.core.RedisTemplate;

import jakarta.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * 设备服务
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/7/25 10:00
 */
@GrpcService
@Slf4j
public class EndDeviceServer extends EndDeviceServiceGrpc.EndDeviceServiceImplBase {

    public static StreamObserver<EndDeviceProto.SubscribeEndDeviceResponse> RESPONSE_OBSERVER = null;

    @Resource
    private StorageEnergyInfoService storageEnergyInfoService;
    @Resource
    private StorageDynamicStageService storageDynamicStageService;
    @Resource
    private ModbusRequestUtil modbusRequestUtil;
    @Resource
    private EndDeviceService endDeviceService;
    @Resource
    private RedisTemplate<String, Boolean> redisTemplate;
    @Resource
    private StrategyService strategyService;
    @Resource
    private DeviceListService deviceListService;

    @Override
    public void getEndDevices(EndDeviceProto.GetEndDevicesParam request, StreamObserver<EndDeviceProto.GetEndDeviceResponse> responseObserver) {
        List<StorageEnergyInfoDO> deviceList = storageEnergyInfoService.list(Wrappers.<StorageEnergyInfoDO>lambdaQuery().eq(StorageEnergyInfoDO::getGridCompany, request.getElectricPowerCompany()));
        List<EndDeviceProto.EndDevice> list = StorageEnergyInfoDO.buildEndDeviceList(deviceList);
        EndDeviceProto.GetEndDeviceResponse res = EndDeviceProto.GetEndDeviceResponse.newBuilder()
                .addAllEndDevices(list)
                .build();
        responseObserver.onNext(res);
        responseObserver.onCompleted();
    }

    @Override
    public void getEndDevice(EndDeviceProto.GetEndDeviceParam request, StreamObserver<EndDeviceProto.EndDevice> responseObserver) {
        StorageEnergyInfoDO endDevice = storageEnergyInfoService.getByDeviceId(request.getId());
        if (endDevice == null) {
            log.error("device not exists: {}", request.getId());
            responseObserver.onError(Status.INTERNAL.withDescription("device not exists: " + request.getId()).asRuntimeException());
            return;
        }
        List<EndDeviceProto.EndDevice> endDevices = StorageEnergyInfoDO.buildEndDeviceList(Collections.singletonList(endDevice));
        responseObserver.onNext(endDevices.get(0));
        responseObserver.onCompleted();
    }

    @Override
    public void getEndDeviceData(EndDeviceProto.GetEndDeviceParam request, StreamObserver<EndDeviceProto.EndDeviceData> responseObserver) {
        StorageEnergyInfoDO endDevice = storageEnergyInfoService.getByDeviceId(request.getId());
        EndDeviceReadDataDTO res = strategyService.chooseReadData(endDevice.getGridCompany(), endDevice.getDeviceType());
        DeviceListDO deviceInfo = deviceListService.getById(request.getId());
        if (deviceInfo.getState() < 0) {
            log.error("device: {} is offline", deviceInfo.getDeviceSn());
            responseObserver.onNext(EndDeviceProto.EndDeviceData.newBuilder().build());
            responseObserver.onCompleted();
            return;
        }
        try {
            modbusRequestUtil.processDeviceConfig(deviceInfo, res);
        } catch (Exception e) {
            log.error(String.format("device read error: %s", e.getMessage()), e);
            responseObserver.onError(Status.INTERNAL.withDescription(String.format("device read error: %s", e.getMessage()))
                    .asRuntimeException());
            return;
        }
        responseObserver.onNext(res.buildEndDeviceData(endDevice));
        responseObserver.onCompleted();
    }

    @Override
    public void setDefaultControl(EndDeviceProto.DefaultDERControl request, StreamObserver<EmptyProto.Empty> responseObserver) {
        try {
            endDeviceService.setDefaultControl(request);
            responseObserver.onNext(EmptyProto.Empty.newBuilder().build());
        } catch (Exception e) {
            log.error(String.format("device setDefaultControl error: %s", e.getMessage()), e);
            responseObserver.onError(Status.INTERNAL.withDescription(String.format("device setDefaultControl error: %s", e.getMessage()))
                    .asRuntimeException());
            return;
        }
        responseObserver.onCompleted();
    }

    @Override
    public void setControl(EndDeviceProto.DERControlRequest request, StreamObserver<EmptyProto.Empty> responseObserver) {
        try {
            endDeviceService.setControl(request);
            responseObserver.onNext(EmptyProto.Empty.newBuilder().build());
        } catch (Exception e) {
            log.error(String.format("device setControl error: %s", e.getMessage()), e);
            responseObserver.onError(Status.INTERNAL.withDescription(String.format("device setControl error: %s", e.getMessage()))
                    .asRuntimeException());
            return;
        }
        responseObserver.onCompleted();
    }

    @Override
    public void updateStepState(EndDeviceProto.StepStateRequest request, StreamObserver<EmptyProto.Empty> responseObserver) {
        storageDynamicStageService.updateStepState(request);
        responseObserver.onNext(EmptyProto.Empty.newBuilder().build());
        responseObserver.onCompleted();
    }

    @Override
    public void subscribeEndDevice(EmptyProto.Empty request, StreamObserver<EndDeviceProto.SubscribeEndDeviceResponse> responseObserver) {
        EndDeviceServer.RESPONSE_OBSERVER = responseObserver;
    }

    @Override
    public void updateUtilityServerStatus(EndDeviceProto.UtilityServerStatus request, StreamObserver<EmptyProto.Empty> responseObserver) {
        redisTemplate.opsForValue().set(Constants.UTILITY_SERVER_STATUS, request.getOnline());
        responseObserver.onNext(EmptyProto.Empty.newBuilder().build());
        responseObserver.onCompleted();
    }
}
