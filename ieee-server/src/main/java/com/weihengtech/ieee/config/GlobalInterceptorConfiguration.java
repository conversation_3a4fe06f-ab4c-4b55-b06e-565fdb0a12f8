package com.weihengtech.ieee.config;

import com.weihengtech.ieee.interceptor.LogInterceptor;
import io.grpc.ServerInterceptor;
import net.devh.boot.grpc.server.interceptor.GrpcGlobalServerInterceptor;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/7/25 15:21
 */
@Configuration(proxyBeanMethods = false)
public class GlobalInterceptorConfiguration {

    @GrpcGlobalServerInterceptor
    ServerInterceptor myServerInterceptor() {
        return new LogInterceptor();
    }
}
