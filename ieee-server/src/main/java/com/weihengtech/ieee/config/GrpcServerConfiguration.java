package com.weihengtech.ieee.config;

import io.grpc.netty.shaded.io.grpc.netty.NettyServerBuilder;
import lombok.extern.slf4j.Slf4j;
import net.devh.boot.grpc.server.serverfactory.GrpcServerConfigurer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.Executor;
import java.util.concurrent.Executors;

/**
 * gRPC服务器虚拟线程配置
 * 
 * <AUTHOR>
 * @version 1.0
 * @date 2025/9/23
 */
@Configuration
@Slf4j
public class GrpcServerConfiguration {

    /**
     * 配置gRPC服务器使用虚拟线程执行器
     * 替换默认的grpc-default-executor线程池
     */
    @Bean
    public GrpcServerConfigurer grpcServerConfigurer() {
        return serverBuilder -> {
            if (serverBuilder instanceof NettyServerBuilder) {
                NettyServerBuilder nettyServerBuilder = (NettyServerBuilder) serverBuilder;
                
                // 创建虚拟线程执行器
                Executor virtualThreadExecutor = Executors.newVirtualThreadPerTaskExecutor();
                
                // 设置执行器用于处理gRPC请求
                nettyServerBuilder.executor(virtualThreadExecutor);
                
                log.info("gRPC server configured to use virtual threads executor");
            }
        };
    }
}
