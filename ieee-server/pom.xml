<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.weihengtech.ieee</groupId>
        <artifactId>ieee-protocol</artifactId>
        <version>1.0-SNAPSHOT</version>
    </parent>

    <artifactId>ieee-server</artifactId>

    <properties>
        <maven.compiler.source>21</maven.compiler.source>
        <maven.compiler.target>21</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <!-- 内部依赖 -->
        <dependency>
            <groupId>com.weihengtech.ieee</groupId>
            <artifactId>ieee-core</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>net.devh</groupId>
            <artifactId>grpc-server-spring-boot-starter</artifactId>
            <version>3.0.0.RELEASE</version>
        </dependency>
    </dependencies>

    <build>
        <finalName>ieee-server</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <mainClass>com.weihengtech.ieee.IeeeServerApplication</mainClass>
                </configuration>
            </plugin>

            <plugin>
                <groupId>com.spotify</groupId>
                <artifactId>docker-maven-plugin</artifactId>
                <version>1.2.2</version>
                <configuration>
                    <!--指定生成的镜像名-->
                    <imageName>harbor.weiheng-tech.com/product/ieee-server/${project.artifactId}</imageName>
                    <!-- 指定 Dockerfile 路径 , 根据实际情况改动! ${project.basedir}：项目根路径-->
                    <dockerDirectory>${basedir}</dockerDirectory>
                    <imageTags>
                        <imageTag>0.1.8</imageTag>
                    </imageTags>
                    <!--指定远程 docker api地址 ,此举目的是将构建好的镜像推送至远程的Docker服务器 ,  前提是服务器开启远程连接 -->
                    <dockerHost>http://**************:2375</dockerHost>
                    <serverId>harbor</serverId>
                    <!-- 这里是复制 jar 包到 docker 容器指定目录配置 -->
                    <resources>
                        <resource>
                            <targetPath>/</targetPath>
                            <!--jar 包所在的路径  此处配置的 即对应 target 目录-->
                            <directory>${project.build.directory}</directory>
                            <!--用于指定需要复制的文件 需要包含的 jar包 ，这里对应的是 Dockerfile中添加的文件名　-->
                            <include>${project.build.finalName}.jar</include>
                        </resource>
                    </resources>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>